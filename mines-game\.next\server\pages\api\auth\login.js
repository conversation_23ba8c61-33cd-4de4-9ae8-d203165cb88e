"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/auth/login";
exports.ids = ["pages/api/auth/login"];
exports.modules = {

/***/ "(api-node)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticateRequest: () => (/* binding */ authenticateRequest),\n/* harmony export */   checkRateLimit: () => (/* binding */ checkRateLimit),\n/* harmony export */   clearAuthCookie: () => (/* binding */ clearAuthCookie),\n/* harmony export */   extractTokenFromRequest: () => (/* binding */ extractTokenFromRequest),\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   getClientIP: () => (/* binding */ getClientIP),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   loginUser: () => (/* binding */ loginUser),\n/* harmony export */   registerUser: () => (/* binding */ registerUser),\n/* harmony export */   setAuthCookie: () => (/* binding */ setAuthCookie),\n/* harmony export */   validateLoginData: () => (/* binding */ validateLoginData),\n/* harmony export */   validateRegistrationData: () => (/* binding */ validateRegistrationData),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"jsonwebtoken\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"bcryptjs?cd17\");\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./database */ \"(api-node)/./lib/database.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([bcryptjs__WEBPACK_IMPORTED_MODULE_1__]);\nbcryptjs__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// JWT secret - in production, this should be in environment variables\nconst JWT_SECRET = \"your-super-secret-jwt-key-change-this-in-production-make-it-very-long-and-random\" || 0;\nconst JWT_EXPIRES_IN = '7d';\n/**\n * Hash password using bcrypt\n */ async function hashPassword(password) {\n    const saltRounds = 12;\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"].hash(password, saltRounds);\n}\n/**\n * Verify password against hash\n */ async function verifyPassword(password, hash) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"].compare(password, hash);\n}\n/**\n * Generate JWT token for user\n */ function generateToken(user) {\n    const payload = {\n        userId: user.id,\n        username: user.username,\n        email: user.email\n    };\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n        expiresIn: JWT_EXPIRES_IN\n    });\n}\n/**\n * Verify JWT token and return user data\n */ function verifyToken(token) {\n    try {\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET);\n    } catch (error) {\n        return null;\n    }\n}\n/**\n * Extract token from request headers\n */ function extractTokenFromRequest(req) {\n    const authHeader = req.headers.authorization;\n    if (authHeader && authHeader.startsWith('Bearer ')) {\n        return authHeader.substring(7);\n    }\n    // Also check cookies for token\n    const cookieToken = req.cookies.token;\n    if (cookieToken) {\n        return cookieToken;\n    }\n    return null;\n}\n/**\n * Middleware to authenticate requests\n */ function authenticateRequest(req) {\n    const token = extractTokenFromRequest(req);\n    if (!token) {\n        return null;\n    }\n    const decoded = verifyToken(token);\n    if (!decoded || !decoded.userId) {\n        return null;\n    }\n    // Get fresh user data from database\n    const user = _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findById(decoded.userId);\n    return user;\n}\n/**\n * Higher-order function to protect API routes\n */ function withAuth(handler) {\n    return async (req, res)=>{\n        try {\n            const user = authenticateRequest(req);\n            if (!user) {\n                return res.status(401).json({\n                    success: false,\n                    error: 'Authentication required'\n                });\n            }\n            await handler(req, res, user);\n        } catch (error) {\n            console.error('Auth middleware error:', error);\n            res.status(500).json({\n                success: false,\n                error: 'Internal server error'\n            });\n        }\n    };\n}\n/**\n * Validate user registration data\n */ function validateRegistrationData(username, email, password) {\n    // Username validation\n    if (!username || username.length < 3 || username.length > 20) {\n        return 'Username must be between 3 and 20 characters';\n    }\n    if (!/^[a-zA-Z0-9_]+$/.test(username)) {\n        return 'Username can only contain letters, numbers, and underscores';\n    }\n    // Email validation\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    if (!email || !emailRegex.test(email)) {\n        return 'Please provide a valid email address';\n    }\n    // Password validation\n    if (!password || password.length < 8) {\n        return 'Password must be at least 8 characters long';\n    }\n    if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/.test(password)) {\n        return 'Password must contain at least one uppercase letter, one lowercase letter, and one number';\n    }\n    return null;\n}\n/**\n * Validate login data\n */ function validateLoginData(email, password) {\n    if (!email || !password) {\n        return 'Email and password are required';\n    }\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    if (!emailRegex.test(email)) {\n        return 'Please provide a valid email address';\n    }\n    return null;\n}\n/**\n * Register new user\n */ async function registerUser(username, email, password) {\n    try {\n        // Validate input data\n        const validationError = validateRegistrationData(username, email, password);\n        if (validationError) {\n            return {\n                success: false,\n                error: validationError\n            };\n        }\n        // Check if user already exists\n        const existingUserByEmail = _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByEmail(email);\n        if (existingUserByEmail) {\n            return {\n                success: false,\n                error: 'Email already registered'\n            };\n        }\n        const existingUserByUsername = _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByUsername(username);\n        if (existingUserByUsername) {\n            return {\n                success: false,\n                error: 'Username already taken'\n            };\n        }\n        // Hash password\n        const passwordHash = await hashPassword(password);\n        // Create user\n        const user = _database__WEBPACK_IMPORTED_MODULE_2__.userDb.create(username, email, passwordHash);\n        // Remove password hash from response\n        const { password_hash, ...userWithoutPassword } = user;\n        return {\n            success: true,\n            user: userWithoutPassword\n        };\n    } catch (error) {\n        console.error('Registration error:', error);\n        return {\n            success: false,\n            error: 'Failed to register user'\n        };\n    }\n}\n/**\n * Login user\n */ async function loginUser(email, password) {\n    try {\n        // Validate input data\n        const validationError = validateLoginData(email, password);\n        if (validationError) {\n            return {\n                success: false,\n                error: validationError\n            };\n        }\n        // Find user by email\n        const user = _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByEmail(email);\n        if (!user) {\n            return {\n                success: false,\n                error: 'Invalid email or password'\n            };\n        }\n        // Verify password\n        const isValidPassword = await verifyPassword(password, user.password_hash);\n        if (!isValidPassword) {\n            return {\n                success: false,\n                error: 'Invalid email or password'\n            };\n        }\n        // Generate token\n        const token = generateToken(user);\n        // Remove password hash from response\n        const { password_hash, ...userWithoutPassword } = user;\n        return {\n            success: true,\n            user: userWithoutPassword,\n            token\n        };\n    } catch (error) {\n        console.error('Login error:', error);\n        return {\n            success: false,\n            error: 'Failed to login'\n        };\n    }\n}\n/**\n * Set authentication cookie\n */ function setAuthCookie(res, token) {\n    const isProduction = \"development\" === 'production';\n    res.setHeader('Set-Cookie', [\n        `token=${token}; HttpOnly; Path=/; Max-Age=${7 * 24 * 60 * 60}; SameSite=Strict${isProduction ? '; Secure' : ''}`\n    ]);\n}\n/**\n * Clear authentication cookie\n */ function clearAuthCookie(res) {\n    res.setHeader('Set-Cookie', [\n        'token=; HttpOnly; Path=/; Max-Age=0; SameSite=Strict'\n    ]);\n}\n/**\n * Rate limiting for authentication endpoints\n */ const rateLimitMap = new Map();\nfunction checkRateLimit(ip, maxAttempts = 5, windowMs = 15 * 60 * 1000) {\n    const now = Date.now();\n    const record = rateLimitMap.get(ip);\n    if (!record || now > record.resetTime) {\n        rateLimitMap.set(ip, {\n            count: 1,\n            resetTime: now + windowMs\n        });\n        return true;\n    }\n    if (record.count >= maxAttempts) {\n        return false;\n    }\n    record.count++;\n    return true;\n}\n/**\n * Get client IP address\n */ function getClientIP(req) {\n    const forwarded = req.headers['x-forwarded-for'];\n    const ip = forwarded ? Array.isArray(forwarded) ? forwarded[0] : forwarded.split(',')[0] : req.socket.remoteAddress;\n    return ip || 'unknown';\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./lib/auth.ts\n");

/***/ }),

/***/ "(api-node)/./lib/database.ts":
/*!*************************!*\
  !*** ./lib/database.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   closeDatabase: () => (/* binding */ closeDatabase),\n/* harmony export */   gameDb: () => (/* binding */ gameDb),\n/* harmony export */   gameSessionDb: () => (/* binding */ gameSessionDb),\n/* harmony export */   getDatabase: () => (/* binding */ getDatabase),\n/* harmony export */   initDatabase: () => (/* binding */ initDatabase),\n/* harmony export */   leaderboardDb: () => (/* binding */ leaderboardDb),\n/* harmony export */   transactionDb: () => (/* binding */ transactionDb),\n/* harmony export */   userDb: () => (/* binding */ userDb),\n/* harmony export */   userStatsDb: () => (/* binding */ userStatsDb)\n/* harmony export */ });\n/* harmony import */ var better_sqlite3__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! better-sqlite3 */ \"better-sqlite3\");\n/* harmony import */ var better_sqlite3__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(better_sqlite3__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Database instance\nlet db = null;\n/**\n * Initialize database connection and create tables\n */ function initDatabase() {\n    if (db) return db;\n    const dbPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'data', 'mines.db');\n    try {\n        // Ensure data directory exists\n        const fs = __webpack_require__(/*! fs */ \"fs\");\n        const dataDir = path__WEBPACK_IMPORTED_MODULE_1___default().dirname(dbPath);\n        if (!fs.existsSync(dataDir)) {\n            fs.mkdirSync(dataDir, {\n                recursive: true\n            });\n        }\n        db = new (better_sqlite3__WEBPACK_IMPORTED_MODULE_0___default())(dbPath);\n        // Enable WAL mode for better performance\n        db.pragma('journal_mode = WAL');\n        db.pragma('synchronous = NORMAL');\n        db.pragma('cache_size = 1000000');\n        db.pragma('temp_store = memory');\n        createTables();\n        console.log('Database initialized successfully');\n        return db;\n    } catch (error) {\n        console.error('Failed to initialize database:', error);\n        throw error;\n    }\n}\n/**\n * Create database tables\n */ function createTables() {\n    if (!db) throw new Error('Database not initialized');\n    // Users table\n    db.exec(`\n    CREATE TABLE IF NOT EXISTS users (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      username TEXT UNIQUE NOT NULL,\n      email TEXT UNIQUE NOT NULL,\n      password_hash TEXT NOT NULL,\n      usdt_balance REAL DEFAULT 0.0,\n      ltc_balance REAL DEFAULT 0.0,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP\n    )\n  `);\n    // Check if games table exists and what schema it has\n    const tableExists = db.prepare(\"SELECT name FROM sqlite_master WHERE type='table' AND name='games'\").get();\n    if (tableExists) {\n        // Check if we need to migrate old games table\n        const tableInfo = db.prepare(\"PRAGMA table_info(games)\").all();\n        const hasOldColumns = tableInfo.some((col)=>[\n                'grid_size',\n                'mine_count',\n                'revealed_cells',\n                'mine_positions'\n            ].includes(col.name));\n        const hasGameType = tableInfo.some((col)=>col.name === 'game_type');\n        const hasGameData = tableInfo.some((col)=>col.name === 'game_data');\n        if (hasOldColumns || !hasGameType || !hasGameData) {\n            console.log('🔄 Migrating games table to support multiple game types...');\n            migrateGamesTable();\n        }\n    } else {\n        // Create new table with correct schema\n        console.log('📋 Creating new games table with multi-game support...');\n        db.exec(`\n      CREATE TABLE games (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER NOT NULL,\n        game_type TEXT NOT NULL DEFAULT 'mines',\n        bet_amount REAL NOT NULL,\n        current_multiplier REAL DEFAULT 1.0,\n        status TEXT CHECK(status IN ('active', 'won', 'lost', 'cashed_out', 'cancelled')) DEFAULT 'active',\n        server_seed TEXT NOT NULL,\n        client_seed TEXT NOT NULL,\n        server_seed_hash TEXT DEFAULT '',\n        profit REAL DEFAULT 0.0,\n        game_data TEXT DEFAULT '{}',\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n    }\n    // Transactions table\n    db.exec(`\n    CREATE TABLE IF NOT EXISTS transactions (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      user_id INTEGER NOT NULL,\n      game_id INTEGER,\n      type TEXT CHECK(type IN ('deposit', 'withdraw', 'bet', 'win')) NOT NULL,\n      currency TEXT CHECK(currency IN ('USDT', 'LTC')) NOT NULL,\n      amount REAL NOT NULL,\n      status TEXT CHECK(status IN ('pending', 'completed', 'failed')) DEFAULT 'pending',\n      transaction_hash TEXT,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (user_id) REFERENCES users (id),\n      FOREIGN KEY (game_id) REFERENCES games (id)\n    )\n  `);\n    // Phase 2: Enhanced tables for leaderboards, statistics, and sessions\n    // User Statistics table - tracks detailed user performance\n    db.exec(`\n    CREATE TABLE IF NOT EXISTS user_statistics (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      user_id INTEGER NOT NULL,\n      game_type TEXT NOT NULL,\n      total_games INTEGER DEFAULT 0,\n      total_wins INTEGER DEFAULT 0,\n      total_losses INTEGER DEFAULT 0,\n      total_wagered REAL DEFAULT 0.0,\n      total_profit REAL DEFAULT 0.0,\n      biggest_win REAL DEFAULT 0.0,\n      biggest_loss REAL DEFAULT 0.0,\n      highest_multiplier REAL DEFAULT 0.0,\n      current_streak INTEGER DEFAULT 0,\n      best_win_streak INTEGER DEFAULT 0,\n      best_loss_streak INTEGER DEFAULT 0,\n      last_played DATETIME,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (user_id) REFERENCES users (id),\n      UNIQUE(user_id, game_type)\n    )\n  `);\n    // Leaderboards table - tracks top performers\n    db.exec(`\n    CREATE TABLE IF NOT EXISTS leaderboards (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      user_id INTEGER NOT NULL,\n      username TEXT NOT NULL,\n      game_type TEXT NOT NULL,\n      category TEXT NOT NULL, -- 'profit', 'multiplier', 'streak', 'volume'\n      value REAL NOT NULL,\n      rank_position INTEGER,\n      period TEXT NOT NULL, -- 'daily', 'weekly', 'monthly', 'all_time'\n      period_start DATETIME NOT NULL,\n      period_end DATETIME NOT NULL,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (user_id) REFERENCES users (id),\n      UNIQUE(user_id, game_type, category, period, period_start)\n    )\n  `);\n    // Game Sessions table - tracks user gaming sessions\n    db.exec(`\n    CREATE TABLE IF NOT EXISTS game_sessions (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      user_id INTEGER NOT NULL,\n      session_start DATETIME DEFAULT CURRENT_TIMESTAMP,\n      session_end DATETIME,\n      total_games INTEGER DEFAULT 0,\n      total_wagered REAL DEFAULT 0.0,\n      total_profit REAL DEFAULT 0.0,\n      games_won INTEGER DEFAULT 0,\n      games_lost INTEGER DEFAULT 0,\n      biggest_win REAL DEFAULT 0.0,\n      biggest_loss REAL DEFAULT 0.0,\n      is_active BOOLEAN DEFAULT 1,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (user_id) REFERENCES users (id)\n    )\n  `);\n    // Achievements table - tracks user achievements and badges\n    db.exec(`\n    CREATE TABLE IF NOT EXISTS achievements (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      code TEXT UNIQUE NOT NULL,\n      name TEXT NOT NULL,\n      description TEXT NOT NULL,\n      icon TEXT NOT NULL,\n      category TEXT NOT NULL, -- 'wins', 'profit', 'streak', 'volume', 'special'\n      requirement_type TEXT NOT NULL, -- 'count', 'value', 'streak'\n      requirement_value REAL NOT NULL,\n      reward_type TEXT, -- 'badge', 'bonus', 'title'\n      reward_value REAL DEFAULT 0.0,\n      is_active BOOLEAN DEFAULT 1,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP\n    )\n  `);\n    // User Achievements table - tracks which achievements users have earned\n    db.exec(`\n    CREATE TABLE IF NOT EXISTS user_achievements (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      user_id INTEGER NOT NULL,\n      achievement_id INTEGER NOT NULL,\n      earned_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      progress REAL DEFAULT 0.0,\n      is_completed BOOLEAN DEFAULT 0,\n      FOREIGN KEY (user_id) REFERENCES users (id),\n      FOREIGN KEY (achievement_id) REFERENCES achievements (id),\n      UNIQUE(user_id, achievement_id)\n    )\n  `);\n    // Create indexes for better performance\n    db.exec(`\n    CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);\n    CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);\n    CREATE INDEX IF NOT EXISTS idx_games_user_id ON games(user_id);\n    CREATE INDEX IF NOT EXISTS idx_games_status ON games(status);\n    CREATE INDEX IF NOT EXISTS idx_games_type ON games(game_type);\n    CREATE INDEX IF NOT EXISTS idx_games_user_type ON games(user_id, game_type);\n    CREATE INDEX IF NOT EXISTS idx_games_user_status ON games(user_id, status);\n    CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id);\n    CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(type);\n\n    -- Phase 2 indexes\n    CREATE INDEX IF NOT EXISTS idx_user_statistics_user_game ON user_statistics(user_id, game_type);\n    CREATE INDEX IF NOT EXISTS idx_leaderboards_game_category ON leaderboards(game_type, category);\n    CREATE INDEX IF NOT EXISTS idx_leaderboards_period ON leaderboards(period, period_start);\n    CREATE INDEX IF NOT EXISTS idx_leaderboards_rank ON leaderboards(rank_position);\n    CREATE INDEX IF NOT EXISTS idx_game_sessions_user_active ON game_sessions(user_id, is_active);\n    CREATE INDEX IF NOT EXISTS idx_game_sessions_start ON game_sessions(session_start);\n    CREATE INDEX IF NOT EXISTS idx_achievements_category ON achievements(category);\n    CREATE INDEX IF NOT EXISTS idx_user_achievements_user ON user_achievements(user_id);\n    CREATE INDEX IF NOT EXISTS idx_user_achievements_completed ON user_achievements(is_completed);\n  `);\n    // Create triggers for updated_at\n    db.exec(`\n    CREATE TRIGGER IF NOT EXISTS update_users_timestamp\n    AFTER UPDATE ON users\n    BEGIN\n      UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;\n    END;\n  `);\n    db.exec(`\n    CREATE TRIGGER IF NOT EXISTS update_games_timestamp\n    AFTER UPDATE ON games\n    BEGIN\n      UPDATE games SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;\n    END;\n  `);\n    // Phase 2 triggers\n    db.exec(`\n    CREATE TRIGGER IF NOT EXISTS update_user_statistics_timestamp\n    AFTER UPDATE ON user_statistics\n    BEGIN\n      UPDATE user_statistics SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;\n    END;\n  `);\n    db.exec(`\n    CREATE TRIGGER IF NOT EXISTS update_leaderboards_timestamp\n    AFTER UPDATE ON leaderboards\n    BEGIN\n      UPDATE leaderboards SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;\n    END;\n  `);\n    db.exec(`\n    CREATE TRIGGER IF NOT EXISTS update_game_sessions_timestamp\n    AFTER UPDATE ON game_sessions\n    BEGIN\n      UPDATE game_sessions SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;\n    END;\n  `);\n}\n/**\n * Migrate old games table to support multiple game types\n */ function migrateGamesTable() {\n    if (!db) throw new Error('Database not initialized');\n    try {\n        // Check if old columns exist\n        const tableInfo = db.prepare(\"PRAGMA table_info(games)\").all();\n        const hasOldColumns = tableInfo.some((col)=>[\n                'grid_size',\n                'mine_count',\n                'revealed_cells',\n                'mine_positions'\n            ].includes(col.name));\n        if (hasOldColumns) {\n            console.log('📦 Migrating existing mines games...');\n            // Disable foreign key constraints during migration\n            db.exec('PRAGMA foreign_keys = OFF');\n            // First, backup existing data\n            const existingGames = db.prepare(`\n        SELECT * FROM games\n      `).all();\n            console.log(`📋 Found ${existingGames.length} existing games to migrate`);\n            // Create new table with correct schema (without foreign key for now)\n            db.exec(`\n        CREATE TABLE games_new (\n          id INTEGER PRIMARY KEY AUTOINCREMENT,\n          user_id INTEGER NOT NULL,\n          game_type TEXT NOT NULL DEFAULT 'mines',\n          bet_amount REAL NOT NULL,\n          current_multiplier REAL DEFAULT 1.0,\n          status TEXT CHECK(status IN ('active', 'won', 'lost', 'cashed_out', 'cancelled')) DEFAULT 'active',\n          server_seed TEXT NOT NULL,\n          client_seed TEXT NOT NULL,\n          server_seed_hash TEXT DEFAULT '',\n          profit REAL DEFAULT 0.0,\n          game_data TEXT DEFAULT '{}',\n          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP\n        )\n      `);\n            // Migrate data to new table\n            for (const game of existingGames){\n                const gameData = {\n                    grid_size: game.grid_size || 25,\n                    mine_count: game.mine_count,\n                    revealed_cells: JSON.parse(game.revealed_cells || '[]'),\n                    mine_positions: JSON.parse(game.mine_positions || '[]')\n                };\n                db.prepare(`\n          INSERT INTO games_new (\n            id, user_id, game_type, bet_amount, current_multiplier,\n            status, server_seed, client_seed, server_seed_hash, profit,\n            game_data, created_at, updated_at\n          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n        `).run(game.id, game.user_id, 'mines', game.bet_amount, game.current_multiplier, game.status, game.server_seed, game.client_seed, game.server_seed_hash || '', game.profit, JSON.stringify(gameData), game.created_at, game.updated_at);\n            }\n            // Drop old table and rename new one\n            db.exec(`DROP TABLE games`);\n            db.exec(`ALTER TABLE games_new RENAME TO games`);\n            // Recreate indexes\n            db.exec(`\n        CREATE INDEX IF NOT EXISTS idx_games_user_id ON games(user_id);\n        CREATE INDEX IF NOT EXISTS idx_games_status ON games(status);\n        CREATE INDEX IF NOT EXISTS idx_games_type ON games(game_type);\n        CREATE INDEX IF NOT EXISTS idx_games_user_type ON games(user_id, game_type);\n        CREATE INDEX IF NOT EXISTS idx_games_user_status ON games(user_id, status);\n      `);\n            // Recreate trigger\n            db.exec(`\n        CREATE TRIGGER IF NOT EXISTS update_games_timestamp\n        AFTER UPDATE ON games\n        BEGIN\n          UPDATE games SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;\n        END;\n      `);\n            // Re-enable foreign key constraints\n            db.exec('PRAGMA foreign_keys = ON');\n            console.log(`✅ Migrated ${existingGames.length} mines games to new schema`);\n        }\n        console.log('✅ Games table migration completed');\n    } catch (error) {\n        console.error('❌ Games table migration failed:', error);\n        throw error;\n    }\n}\n/**\n * Get database instance\n */ function getDatabase() {\n    if (!db) {\n        return initDatabase();\n    }\n    return db;\n}\n/**\n * Close database connection\n */ function closeDatabase() {\n    if (db) {\n        db.close();\n        db = null;\n    }\n}\n/**\n * User database operations\n */ const userDb = {\n    create: (username, email, passwordHash)=>{\n        const db = getDatabase();\n        const stmt = db.prepare(`\n      INSERT INTO users (username, email, password_hash)\n      VALUES (?, ?, ?)\n    `);\n        const result = stmt.run(username, email, passwordHash);\n        return userDb.findById(result.lastInsertRowid);\n    },\n    findById: (id)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('SELECT * FROM users WHERE id = ?');\n        return stmt.get(id);\n    },\n    findByEmail: (email)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('SELECT * FROM users WHERE email = ?');\n        return stmt.get(email);\n    },\n    findByUsername: (username)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('SELECT * FROM users WHERE username = ?');\n        return stmt.get(username);\n    },\n    updateBalance: (userId, currency, amount)=>{\n        const db = getDatabase();\n        const column = currency === 'USDT' ? 'usdt_balance' : 'ltc_balance';\n        const stmt = db.prepare(`UPDATE users SET ${column} = ? WHERE id = ?`);\n        const result = stmt.run(amount, userId);\n        return result.changes > 0;\n    },\n    addToBalance: (userId, currency, amount)=>{\n        const db = getDatabase();\n        const column = currency === 'USDT' ? 'usdt_balance' : 'ltc_balance';\n        const stmt = db.prepare(`UPDATE users SET ${column} = ${column} + ? WHERE id = ?`);\n        const result = stmt.run(amount, userId);\n        return result.changes > 0;\n    }\n};\n/**\n * Game database operations\n */ const gameDb = {\n    create: (gameData)=>{\n        const db = getDatabase();\n        // Extract game-specific data\n        const { game_type, user_id, bet_amount, current_multiplier, status, server_seed, client_seed, profit, ...specificData } = gameData;\n        const stmt = db.prepare(`\n      INSERT INTO games (\n        user_id, game_type, bet_amount, current_multiplier,\n        status, server_seed, client_seed, server_seed_hash, profit, game_data\n      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n    `);\n        const result = stmt.run(user_id, game_type, bet_amount, current_multiplier, status, server_seed, client_seed, '', profit, JSON.stringify(specificData));\n        return gameDb.findById(result.lastInsertRowid);\n    },\n    findById: (id)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('SELECT * FROM games WHERE id = ?');\n        const game = stmt.get(id);\n        if (!game) return null;\n        return gameDb.parseGameData(game);\n    },\n    update: (id, updates)=>{\n        const db = getDatabase();\n        // Separate base fields from game-specific data\n        const { game_type, user_id, bet_amount, current_multiplier, status, server_seed, client_seed, profit, ...specificData } = updates;\n        const baseFields = {};\n        if (bet_amount !== undefined) baseFields.bet_amount = bet_amount;\n        if (current_multiplier !== undefined) baseFields.current_multiplier = current_multiplier;\n        if (status !== undefined) baseFields.status = status;\n        if (profit !== undefined) baseFields.profit = profit;\n        // If there's game-specific data, update game_data field\n        if (Object.keys(specificData).length > 0) {\n            // Get current game data and merge\n            const currentGame = gameDb.findById(id);\n            if (currentGame) {\n                const currentSpecificData = gameDb.extractGameSpecificData(currentGame);\n                const mergedData = {\n                    ...currentSpecificData,\n                    ...specificData\n                };\n                baseFields.game_data = JSON.stringify(mergedData);\n            }\n        }\n        if (Object.keys(baseFields).length === 0) return false;\n        const fields = Object.keys(baseFields);\n        const setClause = fields.map((field)=>`${field} = ?`).join(', ');\n        const values = fields.map((field)=>baseFields[field]);\n        const stmt = db.prepare(`UPDATE games SET ${setClause} WHERE id = ?`);\n        const result = stmt.run(...values, id);\n        return result.changes > 0;\n    },\n    findActiveByUserId: (userId)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('SELECT * FROM games WHERE user_id = ? AND status = ? ORDER BY created_at DESC LIMIT 1');\n        const game = stmt.get(userId, 'active');\n        if (!game) return null;\n        return gameDb.parseGameData(game);\n    },\n    findByUserId: (userId, limit = 50, gameType)=>{\n        const db = getDatabase();\n        let query = 'SELECT * FROM games WHERE user_id = ?';\n        const params = [\n            userId\n        ];\n        if (gameType) {\n            query += ' AND game_type = ?';\n            params.push(gameType);\n        }\n        query += ' ORDER BY created_at DESC LIMIT ?';\n        params.push(limit);\n        const stmt = db.prepare(query);\n        const games = stmt.all(...params);\n        return games.map((game)=>gameDb.parseGameData(game));\n    },\n    // Helper method to parse game data from database\n    parseGameData: (dbGame)=>{\n        const gameData = JSON.parse(dbGame.game_data || '{}');\n        return {\n            ...dbGame,\n            ...gameData\n        };\n    },\n    // Helper method to extract game-specific data\n    extractGameSpecificData: (gameState)=>{\n        const { id, user_id, game_type, bet_amount, current_multiplier, status, server_seed, client_seed, profit, created_at, updated_at, ...specificData } = gameState;\n        return specificData;\n    }\n};\n/**\n * Transaction database operations\n */ const transactionDb = {\n    create: (transactionData)=>{\n        const db = getDatabase();\n        const stmt = db.prepare(`\n      INSERT INTO transactions (user_id, game_id, type, currency, amount, status, transaction_hash)\n      VALUES (?, ?, ?, ?, ?, ?, ?)\n    `);\n        const result = stmt.run(transactionData.user_id, transactionData.game_id || null, transactionData.type, transactionData.currency, transactionData.amount, transactionData.status, transactionData.transaction_hash || null);\n        return transactionDb.findById(result.lastInsertRowid);\n    },\n    findById: (id)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('SELECT * FROM transactions WHERE id = ?');\n        return stmt.get(id);\n    },\n    findByUserId: (userId, limit = 50)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('SELECT * FROM transactions WHERE user_id = ? ORDER BY created_at DESC LIMIT ?');\n        return stmt.all(userId, limit);\n    },\n    updateStatus: (id, status)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('UPDATE transactions SET status = ? WHERE id = ?');\n        const result = stmt.run(status, id);\n        return result.changes > 0;\n    }\n};\n/**\n * Phase 2: User Statistics database operations\n */ const userStatsDb = {\n    create: (userId, gameType)=>{\n        const db = getDatabase();\n        const stmt = db.prepare(`\n      INSERT INTO user_statistics (user_id, game_type)\n      VALUES (?, ?)\n    `);\n        const result = stmt.run(userId, gameType);\n        return userStatsDb.findByUserAndGame(userId, gameType);\n    },\n    findByUserAndGame: (userId, gameType)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('SELECT * FROM user_statistics WHERE user_id = ? AND game_type = ?');\n        return stmt.get(userId, gameType);\n    },\n    findByUser: (userId)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('SELECT * FROM user_statistics WHERE user_id = ?');\n        return stmt.all(userId);\n    },\n    updateStats: (userId, gameType, updates)=>{\n        const db = getDatabase();\n        const existing = userStatsDb.findByUserAndGame(userId, gameType);\n        if (!existing) {\n            userStatsDb.create(userId, gameType);\n        }\n        const fields = Object.keys(updates).map((key)=>`${key} = ?`).join(', ');\n        const values = Object.values(updates);\n        const stmt = db.prepare(`\n      UPDATE user_statistics\n      SET ${fields}, last_played = CURRENT_TIMESTAMP\n      WHERE user_id = ? AND game_type = ?\n    `);\n        const result = stmt.run(...values, userId, gameType);\n        return result.changes > 0;\n    },\n    incrementStats: (userId, gameType, increments)=>{\n        const db = getDatabase();\n        const existing = userStatsDb.findByUserAndGame(userId, gameType);\n        if (!existing) {\n            userStatsDb.create(userId, gameType);\n        }\n        const fields = Object.keys(increments).map((key)=>`${key} = ${key} + ?`).join(', ');\n        const values = Object.values(increments);\n        const stmt = db.prepare(`\n      UPDATE user_statistics\n      SET ${fields}, last_played = CURRENT_TIMESTAMP\n      WHERE user_id = ? AND game_type = ?\n    `);\n        const result = stmt.run(...values, userId, gameType);\n        return result.changes > 0;\n    }\n};\n/**\n * Phase 2: Leaderboards database operations\n */ const leaderboardDb = {\n    updateEntry: (userId, username, gameType, category, value, period)=>{\n        const db = getDatabase();\n        // Calculate period dates\n        const now = new Date();\n        let periodStart, periodEnd;\n        switch(period){\n            case 'daily':\n                periodStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n                periodEnd = new Date(periodStart.getTime() + 24 * 60 * 60 * 1000);\n                break;\n            case 'weekly':\n                const dayOfWeek = now.getDay();\n                periodStart = new Date(now.getTime() - dayOfWeek * 24 * 60 * 60 * 1000);\n                periodStart.setHours(0, 0, 0, 0);\n                periodEnd = new Date(periodStart.getTime() + 7 * 24 * 60 * 60 * 1000);\n                break;\n            case 'monthly':\n                periodStart = new Date(now.getFullYear(), now.getMonth(), 1);\n                periodEnd = new Date(now.getFullYear(), now.getMonth() + 1, 1);\n                break;\n            default:\n                periodStart = new Date(2024, 0, 1); // Platform start date\n                periodEnd = new Date(2099, 11, 31); // Far future\n        }\n        const stmt = db.prepare(`\n      INSERT OR REPLACE INTO leaderboards\n      (user_id, username, game_type, category, value, period, period_start, period_end)\n      VALUES (?, ?, ?, ?, ?, ?, ?, ?)\n    `);\n        const result = stmt.run(userId, username, gameType, category, value, period, periodStart.toISOString(), periodEnd.toISOString());\n        return result.changes > 0;\n    },\n    getLeaderboard: (gameType, category, period, limit = 10)=>{\n        const db = getDatabase();\n        const stmt = db.prepare(`\n      SELECT * FROM leaderboards\n      WHERE game_type = ? AND category = ? AND period = ?\n      ORDER BY value DESC\n      LIMIT ?\n    `);\n        return stmt.all(gameType, category, period, limit);\n    },\n    getUserRank: (userId, gameType, category, period)=>{\n        const db = getDatabase();\n        const stmt = db.prepare(`\n      SELECT COUNT(*) + 1 as rank FROM leaderboards\n      WHERE game_type = ? AND category = ? AND period = ? AND value > (\n        SELECT COALESCE(value, 0) FROM leaderboards\n        WHERE user_id = ? AND game_type = ? AND category = ? AND period = ?\n      )\n    `);\n        const result = stmt.get(gameType, category, period, userId, gameType, category, period);\n        return result?.rank || 0;\n    }\n};\n/**\n * Phase 2: Game Sessions database operations\n */ const gameSessionDb = {\n    startSession: (userId)=>{\n        const db = getDatabase();\n        // End any existing active session\n        gameSessionDb.endActiveSession(userId);\n        const stmt = db.prepare(`\n      INSERT INTO game_sessions (user_id)\n      VALUES (?)\n    `);\n        const result = stmt.run(userId);\n        return gameSessionDb.findById(result.lastInsertRowid);\n    },\n    findById: (id)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('SELECT * FROM game_sessions WHERE id = ?');\n        return stmt.get(id);\n    },\n    findActiveSession: (userId)=>{\n        const db = getDatabase();\n        const stmt = db.prepare('SELECT * FROM game_sessions WHERE user_id = ? AND is_active = 1 ORDER BY session_start DESC LIMIT 1');\n        return stmt.get(userId);\n    },\n    updateSession: (sessionId, updates)=>{\n        const db = getDatabase();\n        const fields = Object.keys(updates).map((key)=>`${key} = ?`).join(', ');\n        const values = Object.values(updates);\n        const stmt = db.prepare(`UPDATE game_sessions SET ${fields} WHERE id = ?`);\n        const result = stmt.run(...values, sessionId);\n        return result.changes > 0;\n    },\n    endActiveSession: (userId)=>{\n        const db = getDatabase();\n        const stmt = db.prepare(`\n      UPDATE game_sessions\n      SET is_active = 0, session_end = CURRENT_TIMESTAMP\n      WHERE user_id = ? AND is_active = 1\n    `);\n        const result = stmt.run(userId);\n        return result.changes > 0;\n    },\n    getUserSessions: (userId, limit = 20)=>{\n        const db = getDatabase();\n        const stmt = db.prepare(`\n      SELECT * FROM game_sessions\n      WHERE user_id = ?\n      ORDER BY session_start DESC\n      LIMIT ?\n    `);\n        return stmt.all(userId, limit);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./lib/database.ts\n");

/***/ }),

/***/ "(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Flogin&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cauth%5Clogin.ts&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Flogin&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cauth%5Clogin.ts&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/./node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_auth_login_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\auth\\login.ts */ \"(api-node)/./pages/api/auth/login.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_pages_api_auth_login_ts__WEBPACK_IMPORTED_MODULE_3__]);\n_pages_api_auth_login_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_auth_login_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_auth_login_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/auth/login\",\n        pathname: \"/api/auth/login\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_auth_login_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaS1ub2RlKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtcm91dGUtbG9hZGVyL2luZGV4LmpzP2tpbmQ9UEFHRVNfQVBJJnBhZ2U9JTJGYXBpJTJGYXV0aCUyRmxvZ2luJnByZWZlcnJlZFJlZ2lvbj0mYWJzb2x1dGVQYWdlUGF0aD0uJTJGcGFnZXMlNUNhcGklNUNhdXRoJTVDbG9naW4udHMmbWlkZGxld2FyZUNvbmZpZ0Jhc2U2ND1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQStGO0FBQ3ZDO0FBQ0U7QUFDMUQ7QUFDeUQ7QUFDekQ7QUFDQSxpRUFBZSx3RUFBSyxDQUFDLHFEQUFRLFlBQVksRUFBQztBQUMxQztBQUNPLGVBQWUsd0VBQUssQ0FBQyxxREFBUTtBQUNwQztBQUNPLHdCQUF3Qix5R0FBbUI7QUFDbEQ7QUFDQSxjQUFjLGtFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsWUFBWTtBQUNaLENBQUM7O0FBRUQscUMiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQYWdlc0FQSVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9wYWdlcy1hcGkvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBob2lzdCB9IGZyb20gXCJuZXh0L2Rpc3QvYnVpbGQvdGVtcGxhdGVzL2hlbHBlcnNcIjtcbi8vIEltcG9ydCB0aGUgdXNlcmxhbmQgY29kZS5cbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCIuL3BhZ2VzXFxcXGFwaVxcXFxhdXRoXFxcXGxvZ2luLnRzXCI7XG4vLyBSZS1leHBvcnQgdGhlIGhhbmRsZXIgKHNob3VsZCBiZSB0aGUgZGVmYXVsdCBleHBvcnQpLlxuZXhwb3J0IGRlZmF1bHQgaG9pc3QodXNlcmxhbmQsICdkZWZhdWx0Jyk7XG4vLyBSZS1leHBvcnQgY29uZmlnLlxuZXhwb3J0IGNvbnN0IGNvbmZpZyA9IGhvaXN0KHVzZXJsYW5kLCAnY29uZmlnJyk7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBQYWdlc0FQSVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5QQUdFU19BUEksXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9hdXRoL2xvZ2luXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvYXV0aC9sb2dpblwiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6ICcnLFxuICAgICAgICBmaWxlbmFtZTogJydcbiAgICB9LFxuICAgIHVzZXJsYW5kXG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cGFnZXMtYXBpLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Flogin&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cauth%5Clogin.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./pages/api/auth/login.ts":
/*!*********************************!*\
  !*** ./pages/api/auth/login.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/auth */ \"(api-node)/./lib/auth.ts\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database */ \"(api-node)/./lib/database.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_auth__WEBPACK_IMPORTED_MODULE_0__]);\n_lib_auth__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\nasync function handler(req, res) {\n    // Initialize database\n    (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.initDatabase)();\n    if (req.method !== 'POST') {\n        return res.status(405).json({\n            success: false,\n            error: 'Method not allowed'\n        });\n    }\n    try {\n        // Rate limiting\n        const clientIP = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_0__.getClientIP)(req);\n        if (!(0,_lib_auth__WEBPACK_IMPORTED_MODULE_0__.checkRateLimit)(clientIP, 10, 15 * 60 * 1000)) {\n            return res.status(429).json({\n                success: false,\n                error: 'Too many login attempts. Please try again later.'\n            });\n        }\n        const { email, password } = req.body;\n        if (!email || !password) {\n            return res.status(400).json({\n                success: false,\n                error: 'Email and password are required'\n            });\n        }\n        const result = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_0__.loginUser)(email, password);\n        if (result.success && result.user && result.token) {\n            (0,_lib_auth__WEBPACK_IMPORTED_MODULE_0__.setAuthCookie)(res, result.token);\n            return res.status(200).json({\n                success: true,\n                user: result.user,\n                message: 'Login successful'\n            });\n        } else {\n            return res.status(401).json({\n                success: false,\n                error: result.error || 'Invalid credentials'\n            });\n        }\n    } catch (error) {\n        console.error('Login API error:', error);\n        return res.status(500).json({\n            success: false,\n            error: 'Internal server error'\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/auth/login.ts\n");

/***/ }),

/***/ "bcryptjs?cd17":
/*!***************************!*\
  !*** external "bcryptjs" ***!
  \***************************/
/***/ ((module) => {

module.exports = import("bcryptjs");;

/***/ }),

/***/ "better-sqlite3":
/*!*********************************!*\
  !*** external "better-sqlite3" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("better-sqlite3");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "jsonwebtoken":
/*!*******************************!*\
  !*** external "jsonwebtoken" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("jsonwebtoken");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Flogin&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cauth%5Clogin.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();