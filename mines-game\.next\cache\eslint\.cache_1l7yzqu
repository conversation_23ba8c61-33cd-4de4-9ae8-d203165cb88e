[{"E:\\111\\PROJECT\\mines-game\\pages\\api\\auth\\login.ts": "1", "E:\\111\\PROJECT\\mines-game\\pages\\api\\auth\\logout.ts": "2", "E:\\111\\PROJECT\\mines-game\\pages\\api\\auth\\me.ts": "3", "E:\\111\\PROJECT\\mines-game\\pages\\api\\auth\\signup.ts": "4", "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\active.ts": "5", "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\cashout.ts": "6", "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\history.ts": "7", "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\list.ts": "8", "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\move.ts": "9", "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\pick.ts": "10", "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\reset-active.ts": "11", "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\reset-stats.ts": "12", "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\start.ts": "13", "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\stats.ts": "14", "E:\\111\\PROJECT\\mines-game\\pages\\api\\lobby\\stats.ts": "15", "E:\\111\\PROJECT\\mines-game\\pages\\api\\test\\phase2-db.ts": "16", "E:\\111\\PROJECT\\mines-game\\pages\\api\\wallet\\deposit.ts": "17", "E:\\111\\PROJECT\\mines-game\\pages\\api\\wallet\\withdraw.ts": "18", "E:\\111\\PROJECT\\mines-game\\pages\\game\\[gameType].tsx": "19", "E:\\111\\PROJECT\\mines-game\\pages\\index.tsx": "20", "E:\\111\\PROJECT\\mines-game\\pages\\lobby.tsx": "21", "E:\\111\\PROJECT\\mines-game\\pages\\login.tsx": "22", "E:\\111\\PROJECT\\mines-game\\pages\\signup.tsx": "23", "E:\\111\\PROJECT\\mines-game\\pages\\_app.tsx": "24", "E:\\111\\PROJECT\\mines-game\\pages\\_document.tsx": "25", "E:\\111\\PROJECT\\mines-game\\components\\game\\crash\\CrashControls.tsx": "26", "E:\\111\\PROJECT\\mines-game\\components\\game\\crash\\CrashDisplay.tsx": "27", "E:\\111\\PROJECT\\mines-game\\components\\game\\dice\\DiceControls.tsx": "28", "E:\\111\\PROJECT\\mines-game\\components\\game\\dice\\DiceDisplay.tsx": "29", "E:\\111\\PROJECT\\mines-game\\components\\game\\GameControls.tsx": "30", "E:\\111\\PROJECT\\mines-game\\components\\game\\GameGrid.tsx": "31", "E:\\111\\PROJECT\\mines-game\\components\\game\\GameHistory.tsx": "32", "E:\\111\\PROJECT\\mines-game\\components\\game\\GameMessage.tsx": "33", "E:\\111\\PROJECT\\mines-game\\components\\game\\LiveStats.tsx": "34", "E:\\111\\PROJECT\\mines-game\\components\\game\\ProfitLossChart.tsx": "35", "E:\\111\\PROJECT\\mines-game\\components\\ui\\badge.tsx": "36", "E:\\111\\PROJECT\\mines-game\\components\\ui\\button.tsx": "37", "E:\\111\\PROJECT\\mines-game\\components\\ui\\card.tsx": "38", "E:\\111\\PROJECT\\mines-game\\components\\ui\\dialog.tsx": "39", "E:\\111\\PROJECT\\mines-game\\components\\ui\\input.tsx": "40", "E:\\111\\PROJECT\\mines-game\\components\\ui\\select.tsx": "41", "E:\\111\\PROJECT\\mines-game\\components\\ui\\tabs.tsx": "42", "E:\\111\\PROJECT\\mines-game\\components\\ui\\toast.tsx": "43", "E:\\111\\PROJECT\\mines-game\\components\\ui\\toaster.tsx": "44", "E:\\111\\PROJECT\\mines-game\\components\\ui\\use-toast.ts": "45", "E:\\111\\PROJECT\\mines-game\\components\\wallet\\WalletModal.tsx": "46", "E:\\111\\PROJECT\\mines-game\\lib\\auth.ts": "47", "E:\\111\\PROJECT\\mines-game\\lib\\crypto.ts": "48", "E:\\111\\PROJECT\\mines-game\\lib\\database.ts": "49", "E:\\111\\PROJECT\\mines-game\\lib\\game-logic.ts": "50", "E:\\111\\PROJECT\\mines-game\\lib\\games\\BaseGameProvider.ts": "51", "E:\\111\\PROJECT\\mines-game\\lib\\games\\crash\\CrashGameProvider.ts": "52", "E:\\111\\PROJECT\\mines-game\\lib\\games\\dice\\DiceGameProvider.ts": "53", "E:\\111\\PROJECT\\mines-game\\lib\\games\\GameFactory.ts": "54", "E:\\111\\PROJECT\\mines-game\\lib\\games\\mines\\MinesGameProvider.ts": "55", "E:\\111\\PROJECT\\mines-game\\lib\\games\\PlaceholderGameProvider.ts": "56", "E:\\111\\PROJECT\\mines-game\\lib\\games\\registry.ts": "57", "E:\\111\\PROJECT\\mines-game\\lib\\sounds.ts": "58", "E:\\111\\PROJECT\\mines-game\\lib\\utils.ts": "59"}, {"size": 1516, "mtime": 1748129429420, "results": "60", "hashOfConfig": "61"}, {"size": 633, "mtime": 1748129429436, "results": "62", "hashOfConfig": "61"}, {"size": 963, "mtime": 1748129429451, "results": "63", "hashOfConfig": "61"}, {"size": 1894, "mtime": 1748129429436, "results": "64", "hashOfConfig": "61"}, {"size": 1277, "mtime": 1748179028035, "results": "65", "hashOfConfig": "61"}, {"size": 1222, "mtime": 1748129429451, "results": "66", "hashOfConfig": "61"}, {"size": 1474, "mtime": 1748179066188, "results": "67", "hashOfConfig": "61"}, {"size": 1349, "mtime": 1748178998967, "results": "68", "hashOfConfig": "61"}, {"size": 3813, "mtime": 1748178869064, "results": "69", "hashOfConfig": "61"}, {"size": 1672, "mtime": 1748165690016, "results": "70", "hashOfConfig": "61"}, {"size": 1344, "mtime": 1748240208584, "results": "71", "hashOfConfig": "61"}, {"size": 1112, "mtime": 1748285876067, "results": "72", "hashOfConfig": "61"}, {"size": 5875, "mtime": 1748804767082, "results": "73", "hashOfConfig": "61"}, {"size": 3611, "mtime": 1748286262163, "results": "74", "hashOfConfig": "61"}, {"size": 2957, "mtime": 1748322475932, "results": "75", "hashOfConfig": "61"}, {"size": 3099, "mtime": 1748286820923, "results": "76", "hashOfConfig": "61"}, {"size": 2612, "mtime": 1748129429404, "results": "77", "hashOfConfig": "61"}, {"size": 2833, "mtime": 1748129429420, "results": "78", "hashOfConfig": "61"}, {"size": 21857, "mtime": 1748804720029, "results": "79", "hashOfConfig": "61"}, {"size": 7308, "mtime": 1748318203995, "results": "80", "hashOfConfig": "61"}, {"size": 20088, "mtime": 1748330067486, "results": "81", "hashOfConfig": "61"}, {"size": 5440, "mtime": 1748331152726, "results": "82", "hashOfConfig": "61"}, {"size": 9914, "mtime": 1748331170884, "results": "83", "hashOfConfig": "61"}, {"size": 1377, "mtime": 1748804619101, "results": "84", "hashOfConfig": "61"}, {"size": 449, "mtime": 1748177584797, "results": "85", "hashOfConfig": "61"}, {"size": 10042, "mtime": 1748804545752, "results": "86", "hashOfConfig": "61"}, {"size": 9148, "mtime": 1748804575197, "results": "87", "hashOfConfig": "61"}, {"size": 9383, "mtime": 1748330794912, "results": "88", "hashOfConfig": "61"}, {"size": 7733, "mtime": 1748323031712, "results": "89", "hashOfConfig": "61"}, {"size": 10643, "mtime": 1748282216660, "results": "90", "hashOfConfig": "61"}, {"size": 4305, "mtime": 1748282445527, "results": "91", "hashOfConfig": "61"}, {"size": 14830, "mtime": 1748362900491, "results": "92", "hashOfConfig": "61"}, {"size": 5072, "mtime": 1748804785499, "results": "93", "hashOfConfig": "61"}, {"size": 9535, "mtime": 1748285914397, "results": "94", "hashOfConfig": "61"}, {"size": 3669, "mtime": 1748283104239, "results": "95", "hashOfConfig": "61"}, {"size": 1128, "mtime": 1748804743653, "results": "96", "hashOfConfig": "61"}, {"size": 1835, "mtime": 1748129429248, "results": "97", "hashOfConfig": "61"}, {"size": 1877, "mtime": 1748129429264, "results": "98", "hashOfConfig": "61"}, {"size": 3835, "mtime": 1748129429279, "results": "99", "hashOfConfig": "61"}, {"size": 824, "mtime": 1748129429295, "results": "100", "hashOfConfig": "61"}, {"size": 5613, "mtime": 1748195415477, "results": "101", "hashOfConfig": "61"}, {"size": 1883, "mtime": 1748129429295, "results": "102", "hashOfConfig": "61"}, {"size": 5040, "mtime": 1748194096566, "results": "103", "hashOfConfig": "61"}, {"size": 780, "mtime": 1748193847531, "results": "104", "hashOfConfig": "61"}, {"size": 3970, "mtime": 1748193839279, "results": "105", "hashOfConfig": "61"}, {"size": 11050, "mtime": 1748129429295, "results": "106", "hashOfConfig": "61"}, {"size": 7754, "mtime": 1748129429389, "results": "107", "hashOfConfig": "61"}, {"size": 5509, "mtime": 1748129429342, "results": "108", "hashOfConfig": "61"}, {"size": 27915, "mtime": 1748286723493, "results": "109", "hashOfConfig": "61"}, {"size": 10084, "mtime": 1748181831031, "results": "110", "hashOfConfig": "61"}, {"size": 6222, "mtime": 1748322864575, "results": "111", "hashOfConfig": "61"}, {"size": 7264, "mtime": 1748804489254, "results": "112", "hashOfConfig": "61"}, {"size": 5754, "mtime": 1748330109468, "results": "113", "hashOfConfig": "61"}, {"size": 6266, "mtime": 1748804592636, "results": "114", "hashOfConfig": "61"}, {"size": 7161, "mtime": 1748178714317, "results": "115", "hashOfConfig": "61"}, {"size": 4480, "mtime": 1748322910594, "results": "116", "hashOfConfig": "61"}, {"size": 4261, "mtime": 1748178645838, "results": "117", "hashOfConfig": "61"}, {"size": 1278, "mtime": 1748129429358, "results": "118", "hashOfConfig": "61"}, {"size": 7029, "mtime": 1748285106978, "results": "119", "hashOfConfig": "61"}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ennnw8", {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 40, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "E:\\111\\PROJECT\\mines-game\\pages\\api\\auth\\login.ts", [], [], "E:\\111\\PROJECT\\mines-game\\pages\\api\\auth\\logout.ts", [], [], "E:\\111\\PROJECT\\mines-game\\pages\\api\\auth\\me.ts", ["297"], [], "E:\\111\\PROJECT\\mines-game\\pages\\api\\auth\\signup.ts", ["298"], [], "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\active.ts", ["299"], [], "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\cashout.ts", [], [], "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\history.ts", [], [], "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\list.ts", ["300"], [], "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\move.ts", ["301", "302"], [], "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\pick.ts", [], [], "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\reset-active.ts", [], [], "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\reset-stats.ts", ["303"], [], "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\start.ts", ["304", "305"], [], "E:\\111\\PROJECT\\mines-game\\pages\\api\\game\\stats.ts", [], [], "E:\\111\\PROJECT\\mines-game\\pages\\api\\lobby\\stats.ts", ["306", "307", "308"], [], "E:\\111\\PROJECT\\mines-game\\pages\\api\\test\\phase2-db.ts", ["309", "310", "311", "312", "313", "314", "315", "316", "317"], [], "E:\\111\\PROJECT\\mines-game\\pages\\api\\wallet\\deposit.ts", [], [], "E:\\111\\PROJECT\\mines-game\\pages\\api\\wallet\\withdraw.ts", [], [], "E:\\111\\PROJECT\\mines-game\\pages\\game\\[gameType].tsx", ["318", "319", "320", "321", "322", "323", "324", "325", "326", "327", "328", "329"], [], "E:\\111\\PROJECT\\mines-game\\pages\\index.tsx", ["330"], [], "E:\\111\\PROJECT\\mines-game\\pages\\lobby.tsx", ["331", "332", "333"], [], "E:\\111\\PROJECT\\mines-game\\pages\\login.tsx", ["334", "335"], [], "E:\\111\\PROJECT\\mines-game\\pages\\signup.tsx", ["336"], [], "E:\\111\\PROJECT\\mines-game\\pages\\_app.tsx", [], [], "E:\\111\\PROJECT\\mines-game\\pages\\_document.tsx", [], [], "E:\\111\\PROJECT\\mines-game\\components\\game\\crash\\CrashControls.tsx", ["337", "338", "339"], [], "E:\\111\\PROJECT\\mines-game\\components\\game\\crash\\CrashDisplay.tsx", [], [], "E:\\111\\PROJECT\\mines-game\\components\\game\\dice\\DiceControls.tsx", ["340", "341", "342", "343", "344", "345", "346", "347"], [], "E:\\111\\PROJECT\\mines-game\\components\\game\\dice\\DiceDisplay.tsx", ["348", "349", "350", "351", "352", "353"], [], "E:\\111\\PROJECT\\mines-game\\components\\game\\GameControls.tsx", ["354", "355", "356", "357", "358", "359"], [], "E:\\111\\PROJECT\\mines-game\\components\\game\\GameGrid.tsx", ["360", "361", "362", "363", "364", "365", "366"], [], "E:\\111\\PROJECT\\mines-game\\components\\game\\GameHistory.tsx", ["367", "368", "369", "370", "371"], [], "E:\\111\\PROJECT\\mines-game\\components\\game\\GameMessage.tsx", [], [], "E:\\111\\PROJECT\\mines-game\\components\\game\\LiveStats.tsx", ["372", "373", "374"], [], "E:\\111\\PROJECT\\mines-game\\components\\game\\ProfitLossChart.tsx", ["375", "376", "377", "378"], [], "E:\\111\\PROJECT\\mines-game\\components\\ui\\badge.tsx", [], [], "E:\\111\\PROJECT\\mines-game\\components\\ui\\button.tsx", [], [], "E:\\111\\PROJECT\\mines-game\\components\\ui\\card.tsx", [], [], "E:\\111\\PROJECT\\mines-game\\components\\ui\\dialog.tsx", [], [], "E:\\111\\PROJECT\\mines-game\\components\\ui\\input.tsx", ["379"], [], "E:\\111\\PROJECT\\mines-game\\components\\ui\\select.tsx", [], [], "E:\\111\\PROJECT\\mines-game\\components\\ui\\tabs.tsx", [], [], "E:\\111\\PROJECT\\mines-game\\components\\ui\\toast.tsx", [], [], "E:\\111\\PROJECT\\mines-game\\components\\ui\\toaster.tsx", [], [], "E:\\111\\PROJECT\\mines-game\\components\\ui\\use-toast.ts", ["380", "381", "382", "383", "384"], [], "E:\\111\\PROJECT\\mines-game\\components\\wallet\\WalletModal.tsx", ["385", "386", "387"], [], "E:\\111\\PROJECT\\mines-game\\lib\\auth.ts", ["388", "389", "390", "391"], [], "E:\\111\\PROJECT\\mines-game\\lib\\crypto.ts", ["392", "393", "394", "395"], [], "E:\\111\\PROJECT\\mines-game\\lib\\database.ts", ["396", "397", "398", "399", "400", "401", "402", "403", "404", "405", "406", "407", "408", "409", "410", "411", "412", "413", "414", "415", "416", "417", "418", "419", "420", "421", "422", "423", "424", "425", "426", "427", "428", "429", "430", "431", "432", "433", "434", "435"], [], "E:\\111\\PROJECT\\mines-game\\lib\\game-logic.ts", ["436"], [], "E:\\111\\PROJECT\\mines-game\\lib\\games\\BaseGameProvider.ts", ["437", "438", "439", "440", "441"], [], "E:\\111\\PROJECT\\mines-game\\lib\\games\\crash\\CrashGameProvider.ts", ["442", "443", "444", "445", "446", "447", "448", "449"], [], "E:\\111\\PROJECT\\mines-game\\lib\\games\\dice\\DiceGameProvider.ts", ["450", "451"], [], "E:\\111\\PROJECT\\mines-game\\lib\\games\\GameFactory.ts", ["452", "453", "454", "455"], [], "E:\\111\\PROJECT\\mines-game\\lib\\games\\mines\\MinesGameProvider.ts", ["456"], [], "E:\\111\\PROJECT\\mines-game\\lib\\games\\PlaceholderGameProvider.ts", ["457", "458", "459", "460", "461"], [], "E:\\111\\PROJECT\\mines-game\\lib\\games\\registry.ts", [], [], "E:\\111\\PROJECT\\mines-game\\lib\\sounds.ts", [], [], "E:\\111\\PROJECT\\mines-game\\lib\\utils.ts", ["462", "463", "464", "465"], [], {"ruleId": "466", "severity": 1, "message": "467", "line": 27, "column": 13, "nodeType": null, "messageId": "468", "endLine": 27, "endColumn": 26}, {"ruleId": "469", "severity": 1, "message": "470", "line": 39, "column": 19, "nodeType": "471", "messageId": "472", "endLine": 39, "endColumn": 42}, {"ruleId": "473", "severity": 1, "message": "474", "line": 29, "column": 24, "nodeType": "475", "messageId": "476", "endLine": 29, "endColumn": 27, "suggestions": "477"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 22, "column": 58, "nodeType": "475", "messageId": "476", "endLine": 22, "endColumn": 61, "suggestions": "478"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 102, "column": 25, "nodeType": "475", "messageId": "476", "endLine": 102, "endColumn": 28, "suggestions": "479"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 122, "column": 45, "nodeType": "475", "messageId": "476", "endLine": 122, "endColumn": 48, "suggestions": "480"}, {"ruleId": "466", "severity": 1, "message": "481", "line": 3, "column": 24, "nodeType": null, "messageId": "468", "endLine": 3, "endColumn": 30}, {"ruleId": "473", "severity": 1, "message": "474", "line": 91, "column": 31, "nodeType": "475", "messageId": "476", "endLine": 91, "endColumn": 34, "suggestions": "482"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 125, "column": 56, "nodeType": "475", "messageId": "476", "endLine": 125, "endColumn": 59, "suggestions": "483"}, {"ruleId": "466", "severity": 1, "message": "484", "line": 5, "column": 75, "nodeType": null, "messageId": "468", "endLine": 5, "endColumn": 79}, {"ruleId": "473", "severity": 1, "message": "474", "line": 63, "column": 57, "nodeType": "475", "messageId": "476", "endLine": 63, "endColumn": 60, "suggestions": "485"}, {"ruleId": "466", "severity": 1, "message": "486", "line": 63, "column": 62, "nodeType": null, "messageId": "468", "endLine": 63, "endColumn": 67}, {"ruleId": "473", "severity": 1, "message": "474", "line": 26, "column": 28, "nodeType": "475", "messageId": "476", "endLine": 26, "endColumn": 31, "suggestions": "487"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 33, "column": 30, "nodeType": "475", "messageId": "476", "endLine": 33, "endColumn": 33, "suggestions": "488"}, {"ruleId": "466", "severity": 1, "message": "489", "line": 40, "column": 13, "nodeType": null, "messageId": "468", "endLine": 40, "endColumn": 21}, {"ruleId": "473", "severity": 1, "message": "474", "line": 43, "column": 21, "nodeType": "475", "messageId": "476", "endLine": 43, "endColumn": 24, "suggestions": "490"}, {"ruleId": "466", "severity": 1, "message": "491", "line": 51, "column": 13, "nodeType": null, "messageId": "468", "endLine": 51, "endColumn": 30}, {"ruleId": "473", "severity": 1, "message": "474", "line": 54, "column": 21, "nodeType": "475", "messageId": "476", "endLine": 54, "endColumn": 24, "suggestions": "492"}, {"ruleId": "466", "severity": 1, "message": "493", "line": 62, "column": 13, "nodeType": null, "messageId": "468", "endLine": 62, "endColumn": 20}, {"ruleId": "473", "severity": 1, "message": "474", "line": 65, "column": 21, "nodeType": "475", "messageId": "476", "endLine": 65, "endColumn": 24, "suggestions": "494"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 78, "column": 19, "nodeType": "475", "messageId": "476", "endLine": 78, "endColumn": 22, "suggestions": "495"}, {"ruleId": "466", "severity": 1, "message": "496", "line": 8, "column": 29, "nodeType": null, "messageId": "468", "endLine": 8, "endColumn": 44}, {"ruleId": "466", "severity": 1, "message": "497", "line": 8, "column": 46, "nodeType": null, "messageId": "468", "endLine": 8, "endColumn": 56}, {"ruleId": "466", "severity": 1, "message": "498", "line": 8, "column": 58, "nodeType": null, "messageId": "468", "endLine": 8, "endColumn": 67}, {"ruleId": "466", "severity": 1, "message": "499", "line": 27, "column": 3, "nodeType": null, "messageId": "468", "endLine": 27, "endColumn": 7}, {"ruleId": "473", "severity": 1, "message": "474", "line": 60, "column": 48, "nodeType": "475", "messageId": "476", "endLine": 60, "endColumn": 51, "suggestions": "500"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 90, "column": 6, "nodeType": "503", "endLine": 90, "endColumn": 16, "suggestions": "504"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 98, "column": 47, "nodeType": "475", "messageId": "476", "endLine": 98, "endColumn": 50, "suggestions": "505"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 216, "column": 41, "nodeType": "475", "messageId": "476", "endLine": 216, "endColumn": 44, "suggestions": "506"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 432, "column": 41, "nodeType": "475", "messageId": "476", "endLine": 432, "endColumn": 44, "suggestions": "507"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 448, "column": 41, "nodeType": "475", "messageId": "476", "endLine": 448, "endColumn": 44, "suggestions": "508"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 463, "column": 41, "nodeType": "475", "messageId": "476", "endLine": 463, "endColumn": 44, "suggestions": "509"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 498, "column": 43, "nodeType": "475", "messageId": "476", "endLine": 498, "endColumn": 46, "suggestions": "510"}, {"ruleId": "511", "severity": 1, "message": "512", "line": 168, "column": 67, "nodeType": "513", "messageId": "514", "suggestions": "515"}, {"ruleId": "466", "severity": 1, "message": "496", "line": 5, "column": 29, "nodeType": null, "messageId": "468", "endLine": 5, "endColumn": 44}, {"ruleId": "466", "severity": 1, "message": "516", "line": 7, "column": 16, "nodeType": null, "messageId": "468", "endLine": 7, "endColumn": 27}, {"ruleId": "501", "severity": 1, "message": "517", "line": 88, "column": 6, "nodeType": "503", "endLine": 88, "endColumn": 12, "suggestions": "518"}, {"ruleId": "466", "severity": 1, "message": "519", "line": 38, "column": 14, "nodeType": null, "messageId": "468", "endLine": 38, "endColumn": 17}, {"ruleId": "511", "severity": 1, "message": "512", "line": 133, "column": 20, "nodeType": "513", "messageId": "514", "suggestions": "520"}, {"ruleId": "466", "severity": 1, "message": "519", "line": 63, "column": 14, "nodeType": null, "messageId": "468", "endLine": 63, "endColumn": 17}, {"ruleId": "466", "severity": 1, "message": "521", "line": 5, "column": 10, "nodeType": null, "messageId": "468", "endLine": 5, "endColumn": 15}, {"ruleId": "466", "severity": 1, "message": "522", "line": 7, "column": 30, "nodeType": null, "messageId": "468", "endLine": 7, "endColumn": 35}, {"ruleId": "473", "severity": 1, "message": "474", "line": 20, "column": 24, "nodeType": "475", "messageId": "476", "endLine": 20, "endColumn": 27, "suggestions": "523"}, {"ruleId": "466", "severity": 1, "message": "524", "line": 1, "column": 27, "nodeType": null, "messageId": "468", "endLine": 1, "endColumn": 36}, {"ruleId": "466", "severity": 1, "message": "516", "line": 5, "column": 16, "nodeType": null, "messageId": "468", "endLine": 5, "endColumn": 27}, {"ruleId": "466", "severity": 1, "message": "525", "line": 8, "column": 10, "nodeType": null, "messageId": "468", "endLine": 8, "endColumn": 15}, {"ruleId": "466", "severity": 1, "message": "526", "line": 8, "column": 17, "nodeType": null, "messageId": "468", "endLine": 8, "endColumn": 22}, {"ruleId": "466", "severity": 1, "message": "527", "line": 8, "column": 24, "nodeType": null, "messageId": "468", "endLine": 8, "endColumn": 29}, {"ruleId": "466", "severity": 1, "message": "528", "line": 8, "column": 31, "nodeType": null, "messageId": "468", "endLine": 8, "endColumn": 36}, {"ruleId": "466", "severity": 1, "message": "529", "line": 8, "column": 38, "nodeType": null, "messageId": "468", "endLine": 8, "endColumn": 43}, {"ruleId": "466", "severity": 1, "message": "530", "line": 8, "column": 45, "nodeType": null, "messageId": "468", "endLine": 8, "endColumn": 50}, {"ruleId": "511", "severity": 1, "message": "531", "line": 185, "column": 28, "nodeType": "513", "messageId": "514", "suggestions": "532"}, {"ruleId": "511", "severity": 1, "message": "531", "line": 185, "column": 39, "nodeType": "513", "messageId": "514", "suggestions": "533"}, {"ruleId": "511", "severity": 1, "message": "531", "line": 185, "column": 44, "nodeType": "513", "messageId": "514", "suggestions": "534"}, {"ruleId": "511", "severity": 1, "message": "531", "line": 185, "column": 54, "nodeType": "513", "messageId": "514", "suggestions": "535"}, {"ruleId": "511", "severity": 1, "message": "531", "line": 186, "column": 27, "nodeType": "513", "messageId": "514", "suggestions": "536"}, {"ruleId": "511", "severity": 1, "message": "531", "line": 186, "column": 37, "nodeType": "513", "messageId": "514", "suggestions": "537"}, {"ruleId": "466", "severity": 1, "message": "497", "line": 5, "column": 29, "nodeType": null, "messageId": "468", "endLine": 5, "endColumn": 39}, {"ruleId": "466", "severity": 1, "message": "498", "line": 5, "column": 41, "nodeType": null, "messageId": "468", "endLine": 5, "endColumn": 50}, {"ruleId": "473", "severity": 1, "message": "474", "line": 32, "column": 39, "nodeType": "475", "messageId": "476", "endLine": 32, "endColumn": 42, "suggestions": "538"}, {"ruleId": "466", "severity": 1, "message": "539", "line": 48, "column": 9, "nodeType": null, "messageId": "468", "endLine": 48, "endColumn": 30}, {"ruleId": "466", "severity": 1, "message": "540", "line": 53, "column": 9, "nodeType": null, "messageId": "468", "endLine": 53, "endColumn": 24}, {"ruleId": "466", "severity": 1, "message": "541", "line": 54, "column": 9, "nodeType": null, "messageId": "468", "endLine": 54, "endColumn": 25}, {"ruleId": "473", "severity": 1, "message": "474", "line": 32, "column": 41, "nodeType": "475", "messageId": "476", "endLine": 32, "endColumn": 44, "suggestions": "542"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 52, "column": 41, "nodeType": "475", "messageId": "476", "endLine": 52, "endColumn": 44, "suggestions": "543"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 53, "column": 41, "nodeType": "475", "messageId": "476", "endLine": 53, "endColumn": 44, "suggestions": "544"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 121, "column": 41, "nodeType": "475", "messageId": "476", "endLine": 121, "endColumn": 44, "suggestions": "545"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 122, "column": 45, "nodeType": "475", "messageId": "476", "endLine": 122, "endColumn": 48, "suggestions": "546"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 123, "column": 52, "nodeType": "475", "messageId": "476", "endLine": 123, "endColumn": 55, "suggestions": "547"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 123, "column": 92, "nodeType": "475", "messageId": "476", "endLine": 123, "endColumn": 95, "suggestions": "548"}, {"ruleId": "466", "severity": 1, "message": "497", "line": 4, "column": 29, "nodeType": null, "messageId": "468", "endLine": 4, "endColumn": 39}, {"ruleId": "466", "severity": 1, "message": "498", "line": 4, "column": 41, "nodeType": null, "messageId": "468", "endLine": 4, "endColumn": 50}, {"ruleId": "473", "severity": 1, "message": "474", "line": 132, "column": 38, "nodeType": "475", "messageId": "476", "endLine": 132, "endColumn": 41, "suggestions": "549"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 133, "column": 38, "nodeType": "475", "messageId": "476", "endLine": 133, "endColumn": 41, "suggestions": "550"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 134, "column": 33, "nodeType": "475", "messageId": "476", "endLine": 134, "endColumn": 36, "suggestions": "551"}, {"ruleId": "466", "severity": 1, "message": "552", "line": 8, "column": 3, "nodeType": null, "messageId": "468", "endLine": 8, "endColumn": 13}, {"ruleId": "466", "severity": 1, "message": "553", "line": 9, "column": 3, "nodeType": null, "messageId": "468", "endLine": 9, "endColumn": 15}, {"ruleId": "466", "severity": 1, "message": "554", "line": 16, "column": 26, "nodeType": null, "messageId": "468", "endLine": 16, "endColumn": 39}, {"ruleId": "466", "severity": 1, "message": "555", "line": 31, "column": 45, "nodeType": null, "messageId": "468", "endLine": 31, "endColumn": 50}, {"ruleId": "473", "severity": 1, "message": "474", "line": 31, "column": 54, "nodeType": "475", "messageId": "476", "endLine": 31, "endColumn": 57, "suggestions": "556"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 53, "column": 39, "nodeType": "475", "messageId": "476", "endLine": 53, "endColumn": 42, "suggestions": "557"}, {"ruleId": "466", "severity": 1, "message": "486", "line": 53, "column": 44, "nodeType": null, "messageId": "468", "endLine": 53, "endColumn": 49}, {"ruleId": "558", "severity": 1, "message": "559", "line": 5, "column": 18, "nodeType": "560", "messageId": "561", "endLine": 5, "endColumn": 28, "suggestions": "562"}, {"ruleId": "466", "severity": 1, "message": "563", "line": 2, "column": 13, "nodeType": null, "messageId": "468", "endLine": 2, "endColumn": 28}, {"ruleId": "466", "severity": 1, "message": "564", "line": 3, "column": 10, "nodeType": null, "messageId": "468", "endLine": 3, "endColumn": 13}, {"ruleId": "466", "severity": 1, "message": "565", "line": 3, "column": 20, "nodeType": null, "messageId": "468", "endLine": 3, "endColumn": 32}, {"ruleId": "466", "severity": 1, "message": "566", "line": 5, "column": 10, "nodeType": null, "messageId": "468", "endLine": 5, "endColumn": 12}, {"ruleId": "466", "severity": 1, "message": "567", "line": 17, "column": 7, "nodeType": null, "messageId": "568", "endLine": 17, "endColumn": 18}, {"ruleId": "466", "severity": 1, "message": "569", "line": 8, "column": 56, "nodeType": null, "messageId": "468", "endLine": 8, "endColumn": 68}, {"ruleId": "466", "severity": 1, "message": "570", "line": 57, "column": 14, "nodeType": null, "messageId": "468", "endLine": 57, "endColumn": 19}, {"ruleId": "466", "severity": 1, "message": "570", "line": 102, "column": 14, "nodeType": null, "messageId": "468", "endLine": 102, "endColumn": 19}, {"ruleId": "473", "severity": 1, "message": "474", "line": 42, "column": 45, "nodeType": "475", "messageId": "476", "endLine": 42, "endColumn": 48, "suggestions": "571"}, {"ruleId": "466", "severity": 1, "message": "570", "line": 45, "column": 12, "nodeType": null, "messageId": "468", "endLine": 45, "endColumn": 17}, {"ruleId": "466", "severity": 1, "message": "467", "line": 191, "column": 13, "nodeType": null, "messageId": "468", "endLine": 191, "endColumn": 26}, {"ruleId": "466", "severity": 1, "message": "467", "line": 227, "column": 13, "nodeType": null, "messageId": "468", "endLine": 227, "endColumn": 26}, {"ruleId": "466", "severity": 1, "message": "572", "line": 59, "column": 13, "nodeType": null, "messageId": "468", "endLine": 59, "endColumn": 25}, {"ruleId": "469", "severity": 1, "message": "470", "line": 127, "column": 18, "nodeType": "471", "messageId": "472", "endLine": 127, "endColumn": 37}, {"ruleId": "469", "severity": 1, "message": "470", "line": 135, "column": 18, "nodeType": "471", "messageId": "472", "endLine": 135, "endColumn": 37}, {"ruleId": "466", "severity": 1, "message": "573", "line": 195, "column": 9, "nodeType": null, "messageId": "468", "endLine": 195, "endColumn": 11}, {"ruleId": "469", "severity": 1, "message": "470", "line": 18, "column": 16, "nodeType": "471", "messageId": "472", "endLine": 18, "endColumn": 29}, {"ruleId": "473", "severity": 1, "message": "474", "line": 68, "column": 48, "nodeType": "475", "messageId": "476", "endLine": 68, "endColumn": 51, "suggestions": "574"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 71, "column": 46, "nodeType": "475", "messageId": "476", "endLine": 71, "endColumn": 49, "suggestions": "575"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 72, "column": 46, "nodeType": "475", "messageId": "476", "endLine": 72, "endColumn": 49, "suggestions": "576"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 295, "column": 48, "nodeType": "475", "messageId": "476", "endLine": 295, "endColumn": 51, "suggestions": "577"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 505, "column": 34, "nodeType": "475", "messageId": "476", "endLine": 505, "endColumn": 37, "suggestions": "578"}, {"ruleId": "466", "severity": 1, "message": "579", "line": 516, "column": 13, "nodeType": null, "messageId": "468", "endLine": 516, "endColumn": 22}, {"ruleId": "466", "severity": 1, "message": "580", "line": 516, "column": 24, "nodeType": null, "messageId": "468", "endLine": 516, "endColumn": 31}, {"ruleId": "466", "severity": 1, "message": "581", "line": 516, "column": 73, "nodeType": null, "messageId": "468", "endLine": 516, "endColumn": 84}, {"ruleId": "466", "severity": 1, "message": "582", "line": 516, "column": 86, "nodeType": null, "messageId": "468", "endLine": 516, "endColumn": 97}, {"ruleId": "473", "severity": 1, "message": "474", "line": 518, "column": 23, "nodeType": "475", "messageId": "476", "endLine": 518, "endColumn": 26, "suggestions": "583"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 549, "column": 48, "nodeType": "475", "messageId": "476", "endLine": 549, "endColumn": 51, "suggestions": "584"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 559, "column": 19, "nodeType": "475", "messageId": "476", "endLine": 559, "endColumn": 22, "suggestions": "585"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 570, "column": 42, "nodeType": "475", "messageId": "476", "endLine": 570, "endColumn": 45, "suggestions": "586"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 576, "column": 27, "nodeType": "475", "messageId": "476", "endLine": 576, "endColumn": 30, "suggestions": "587"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 586, "column": 52, "nodeType": "475", "messageId": "476", "endLine": 586, "endColumn": 55, "suggestions": "588"}, {"ruleId": "466", "severity": 1, "message": "589", "line": 587, "column": 13, "nodeType": null, "messageId": "468", "endLine": 587, "endColumn": 15}, {"ruleId": "466", "severity": 1, "message": "580", "line": 587, "column": 17, "nodeType": null, "messageId": "468", "endLine": 587, "endColumn": 24}, {"ruleId": "466", "severity": 1, "message": "579", "line": 587, "column": 26, "nodeType": null, "messageId": "468", "endLine": 587, "endColumn": 35}, {"ruleId": "466", "severity": 1, "message": "590", "line": 587, "column": 37, "nodeType": null, "messageId": "468", "endLine": 587, "endColumn": 47}, {"ruleId": "466", "severity": 1, "message": "591", "line": 587, "column": 49, "nodeType": null, "messageId": "468", "endLine": 587, "endColumn": 67}, {"ruleId": "466", "severity": 1, "message": "592", "line": 587, "column": 69, "nodeType": null, "messageId": "468", "endLine": 587, "endColumn": 75}, {"ruleId": "466", "severity": 1, "message": "581", "line": 587, "column": 77, "nodeType": null, "messageId": "468", "endLine": 587, "endColumn": 88}, {"ruleId": "466", "severity": 1, "message": "582", "line": 587, "column": 90, "nodeType": null, "messageId": "468", "endLine": 587, "endColumn": 101}, {"ruleId": "466", "severity": 1, "message": "593", "line": 587, "column": 103, "nodeType": null, "messageId": "468", "endLine": 587, "endColumn": 109}, {"ruleId": "466", "severity": 1, "message": "594", "line": 587, "column": 111, "nodeType": null, "messageId": "468", "endLine": 587, "endColumn": 121}, {"ruleId": "466", "severity": 1, "message": "595", "line": 587, "column": 123, "nodeType": null, "messageId": "468", "endLine": 587, "endColumn": 133}, {"ruleId": "473", "severity": 1, "message": "474", "line": 640, "column": 47, "nodeType": "475", "messageId": "476", "endLine": 640, "endColumn": 50, "suggestions": "596"}, {"ruleId": "466", "severity": 1, "message": "597", "line": 647, "column": 11, "nodeType": null, "messageId": "468", "endLine": 647, "endColumn": 17}, {"ruleId": "473", "severity": 1, "message": "474", "line": 651, "column": 58, "nodeType": "475", "messageId": "476", "endLine": 651, "endColumn": 61, "suggestions": "598"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 657, "column": 33, "nodeType": "475", "messageId": "476", "endLine": 657, "endColumn": 36, "suggestions": "599"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 663, "column": 60, "nodeType": "475", "messageId": "476", "endLine": 663, "endColumn": 63, "suggestions": "600"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 684, "column": 66, "nodeType": "475", "messageId": "476", "endLine": 684, "endColumn": 69, "suggestions": "601"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 748, "column": 93, "nodeType": "475", "messageId": "476", "endLine": 748, "endColumn": 96, "suggestions": "602"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 770, "column": 96, "nodeType": "475", "messageId": "476", "endLine": 770, "endColumn": 99, "suggestions": "603"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 779, "column": 35, "nodeType": "475", "messageId": "476", "endLine": 779, "endColumn": 38, "suggestions": "604"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 794, "column": 27, "nodeType": "475", "messageId": "476", "endLine": 794, "endColumn": 30, "suggestions": "605"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 800, "column": 40, "nodeType": "475", "messageId": "476", "endLine": 800, "endColumn": 43, "suggestions": "606"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 806, "column": 47, "nodeType": "475", "messageId": "476", "endLine": 806, "endColumn": 50, "suggestions": "607"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 828, "column": 58, "nodeType": "475", "messageId": "476", "endLine": 828, "endColumn": 61, "suggestions": "608"}, {"ruleId": "466", "severity": 1, "message": "609", "line": 1, "column": 21, "nodeType": null, "messageId": "468", "endLine": 1, "endColumn": 25}, {"ruleId": "469", "severity": 1, "message": "470", "line": 68, "column": 20, "nodeType": "471", "messageId": "472", "endLine": 68, "endColumn": 37}, {"ruleId": "473", "severity": 1, "message": "474", "line": 140, "column": 70, "nodeType": "475", "messageId": "476", "endLine": 140, "endColumn": 73, "suggestions": "610"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 151, "column": 46, "nodeType": "475", "messageId": "476", "endLine": 151, "endColumn": 49, "suggestions": "611"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 152, "column": 62, "nodeType": "475", "messageId": "476", "endLine": 152, "endColumn": 65, "suggestions": "612"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 153, "column": 44, "nodeType": "475", "messageId": "476", "endLine": 153, "endColumn": 47, "suggestions": "613"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 29, "column": 37, "nodeType": "475", "messageId": "476", "endLine": 29, "endColumn": 40, "suggestions": "614"}, {"ruleId": "466", "severity": 1, "message": "615", "line": 52, "column": 57, "nodeType": null, "messageId": "468", "endLine": 52, "endColumn": 63}, {"ruleId": "473", "severity": 1, "message": "474", "line": 52, "column": 66, "nodeType": "475", "messageId": "476", "endLine": 52, "endColumn": 69, "suggestions": "616"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 74, "column": 35, "nodeType": "475", "messageId": "476", "endLine": 74, "endColumn": 38, "suggestions": "617"}, {"ruleId": "466", "severity": 1, "message": "618", "line": 127, "column": 54, "nodeType": null, "messageId": "468", "endLine": 127, "endColumn": 61}, {"ruleId": "473", "severity": 1, "message": "474", "line": 127, "column": 63, "nodeType": "475", "messageId": "476", "endLine": 127, "endColumn": 66, "suggestions": "619"}, {"ruleId": "466", "severity": 1, "message": "618", "line": 141, "column": 52, "nodeType": null, "messageId": "468", "endLine": 141, "endColumn": 59}, {"ruleId": "473", "severity": 1, "message": "474", "line": 141, "column": 61, "nodeType": "475", "messageId": "476", "endLine": 141, "endColumn": 64, "suggestions": "620"}, {"ruleId": "466", "severity": 1, "message": "615", "line": 66, "column": 56, "nodeType": null, "messageId": "468", "endLine": 66, "endColumn": 62}, {"ruleId": "473", "severity": 1, "message": "474", "line": 66, "column": 65, "nodeType": "475", "messageId": "476", "endLine": 66, "endColumn": 68, "suggestions": "621"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 82, "column": 13, "nodeType": "475", "messageId": "476", "endLine": 82, "endColumn": 16, "suggestions": "622"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 129, "column": 15, "nodeType": "475", "messageId": "476", "endLine": 129, "endColumn": 18, "suggestions": "623"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 167, "column": 14, "nodeType": "475", "messageId": "476", "endLine": 167, "endColumn": 17, "suggestions": "624"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 201, "column": 56, "nodeType": "475", "messageId": "476", "endLine": 201, "endColumn": 59, "suggestions": "625"}, {"ruleId": "466", "severity": 1, "message": "626", "line": 201, "column": 11, "nodeType": null, "messageId": "468", "endLine": 201, "endColumn": 20}, {"ruleId": "473", "severity": 1, "message": "474", "line": 21, "column": 37, "nodeType": "475", "messageId": "476", "endLine": 21, "endColumn": 40, "suggestions": "627"}, {"ruleId": "466", "severity": 1, "message": "615", "line": 29, "column": 52, "nodeType": null, "messageId": "468", "endLine": 29, "endColumn": 58}, {"ruleId": "473", "severity": 1, "message": "474", "line": 29, "column": 61, "nodeType": "475", "messageId": "476", "endLine": 29, "endColumn": 64, "suggestions": "628"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 36, "column": 35, "nodeType": "475", "messageId": "476", "endLine": 36, "endColumn": 38, "suggestions": "629"}, {"ruleId": "466", "severity": 1, "message": "630", "line": 48, "column": 56, "nodeType": null, "messageId": "468", "endLine": 48, "endColumn": 62}, {"ruleId": "473", "severity": 1, "message": "474", "line": 90, "column": 46, "nodeType": "475", "messageId": "476", "endLine": 90, "endColumn": 49, "suggestions": "631"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 90, "column": 56, "nodeType": "475", "messageId": "476", "endLine": 90, "endColumn": 59, "suggestions": "632"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 104, "column": 46, "nodeType": "475", "messageId": "476", "endLine": 104, "endColumn": 49, "suggestions": "633"}, {"ruleId": "473", "severity": 1, "message": "474", "line": 104, "column": 56, "nodeType": "475", "messageId": "476", "endLine": 104, "endColumn": 59, "suggestions": "634"}, "@typescript-eslint/no-unused-vars", "'password_hash' is assigned a value but never used.", "unusedVar", "@typescript-eslint/no-require-imports", "A `require()` style import is forbidden.", "CallExpression", "noRequireImports", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["635", "636"], ["637", "638"], ["639", "640"], ["641", "642"], "'gameDb' is defined but never used.", ["643", "644"], ["645", "646"], "'user' is defined but never used.", ["647", "648"], "'index' is defined but never used.", ["649", "650"], ["651", "652"], "'testStat' is assigned a value but never used.", ["653", "654"], "'leaderboardUpdate' is assigned a value but never used.", ["655", "656"], "'session' is assigned a value but never used.", ["657", "658"], ["659", "660"], "'CardDescription' is defined but never used.", "'CardHeader' is defined but never used.", "'CardTitle' is defined but never used.", "'Home' is defined but never used.", ["661", "662"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadGameConfig'. Either include it or remove the dependency array.", "ArrayExpression", ["663"], ["664", "665"], ["666", "667"], ["668", "669"], ["670", "671"], ["672", "673"], ["674", "675"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["676", "677", "678", "679"], "'TabsContent' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadLobbyData'. Either include it or remove the dependency array.", ["680"], "'err' is defined but never used.", ["681", "682", "683", "684"], "'Badge' is defined but never used.", "'Timer' is defined but never used.", ["685", "686"], "'useEffect' is defined but never used.", "'Dice1' is defined but never used.", "'Dice2' is defined but never used.", "'Dice3' is defined but never used.", "'Dice4' is defined but never used.", "'Dice5' is defined but never used.", "'Dice6' is defined but never used.", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["687", "688", "689", "690"], ["691", "692", "693", "694"], ["695", "696", "697", "698"], ["699", "700", "701", "702"], ["703", "704", "705", "706"], ["707", "708", "709", "710"], ["711", "712"], "'handleMineCountChange' is assigned a value but never used.", "'quickBetAmounts' is assigned a value but never used.", "'quickMineAmounts' is assigned a value but never used.", ["713", "714"], ["715", "716"], ["717", "718"], ["719", "720"], ["721", "722"], ["723", "724"], ["725", "726"], ["727", "728"], ["729", "730"], ["731", "732"], "'TrendingUp' is defined but never used.", "'TrendingDown' is defined but never used.", "'API_ENDPOINTS' is defined but never used.", "'label' is defined but never used.", ["733", "734"], ["735", "736"], "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["737"], "'ToastPrimitives' is defined but never used.", "'cva' is defined but never used.", "'VariantProps' is defined but never used.", "'cn' is defined but never used.", "'actionTypes' is assigned a value but only used as a type.", "usedOnlyAsType", "'ExternalLink' is defined but never used.", "'error' is defined but never used.", ["738", "739"], "'extendedHash' is assigned a value but never used.", "'iv' is assigned a value but never used.", ["740", "741"], ["742", "743"], ["744", "745"], ["746", "747"], ["748", "749"], "'game_type' is assigned a value but never used.", "'user_id' is assigned a value but never used.", "'server_seed' is assigned a value but never used.", "'client_seed' is assigned a value but never used.", ["750", "751"], ["752", "753"], ["754", "755"], ["756", "757"], ["758", "759"], ["760", "761"], "'id' is assigned a value but never used.", "'bet_amount' is assigned a value but never used.", "'current_multiplier' is assigned a value but never used.", "'status' is assigned a value but never used.", "'profit' is assigned a value but never used.", "'created_at' is assigned a value but never used.", "'updated_at' is assigned a value but never used.", ["762", "763"], "'result' is assigned a value but never used.", ["764", "765"], ["766", "767"], ["768", "769"], ["770", "771"], ["772", "773"], ["774", "775"], ["776", "777"], ["778", "779"], ["780", "781"], ["782", "783"], ["784", "785"], "'User' is defined but never used.", ["786", "787"], ["788", "789"], ["790", "791"], ["792", "793"], ["794", "795"], "'params' is defined but never used.", ["796", "797"], ["798", "799"], "'payload' is defined but never used.", ["800", "801"], ["802", "803"], ["804", "805"], ["806", "807"], ["808", "809"], ["810", "811"], ["812", "813"], "'positions' is assigned a value but never used.", ["814", "815"], ["816", "817"], ["818", "819"], "'action' is defined but never used.", ["820", "821"], ["822", "823"], ["824", "825"], ["826", "827"], {"messageId": "828", "fix": "829", "desc": "830"}, {"messageId": "831", "fix": "832", "desc": "833"}, {"messageId": "828", "fix": "834", "desc": "830"}, {"messageId": "831", "fix": "835", "desc": "833"}, {"messageId": "828", "fix": "836", "desc": "830"}, {"messageId": "831", "fix": "837", "desc": "833"}, {"messageId": "828", "fix": "838", "desc": "830"}, {"messageId": "831", "fix": "839", "desc": "833"}, {"messageId": "828", "fix": "840", "desc": "830"}, {"messageId": "831", "fix": "841", "desc": "833"}, {"messageId": "828", "fix": "842", "desc": "830"}, {"messageId": "831", "fix": "843", "desc": "833"}, {"messageId": "828", "fix": "844", "desc": "830"}, {"messageId": "831", "fix": "845", "desc": "833"}, {"messageId": "828", "fix": "846", "desc": "830"}, {"messageId": "831", "fix": "847", "desc": "833"}, {"messageId": "828", "fix": "848", "desc": "830"}, {"messageId": "831", "fix": "849", "desc": "833"}, {"messageId": "828", "fix": "850", "desc": "830"}, {"messageId": "831", "fix": "851", "desc": "833"}, {"messageId": "828", "fix": "852", "desc": "830"}, {"messageId": "831", "fix": "853", "desc": "833"}, {"messageId": "828", "fix": "854", "desc": "830"}, {"messageId": "831", "fix": "855", "desc": "833"}, {"messageId": "828", "fix": "856", "desc": "830"}, {"messageId": "831", "fix": "857", "desc": "833"}, {"messageId": "828", "fix": "858", "desc": "830"}, {"messageId": "831", "fix": "859", "desc": "833"}, {"desc": "860", "fix": "861"}, {"messageId": "828", "fix": "862", "desc": "830"}, {"messageId": "831", "fix": "863", "desc": "833"}, {"messageId": "828", "fix": "864", "desc": "830"}, {"messageId": "831", "fix": "865", "desc": "833"}, {"messageId": "828", "fix": "866", "desc": "830"}, {"messageId": "831", "fix": "867", "desc": "833"}, {"messageId": "828", "fix": "868", "desc": "830"}, {"messageId": "831", "fix": "869", "desc": "833"}, {"messageId": "828", "fix": "870", "desc": "830"}, {"messageId": "831", "fix": "871", "desc": "833"}, {"messageId": "828", "fix": "872", "desc": "830"}, {"messageId": "831", "fix": "873", "desc": "833"}, {"messageId": "874", "data": "875", "fix": "876", "desc": "877"}, {"messageId": "874", "data": "878", "fix": "879", "desc": "880"}, {"messageId": "874", "data": "881", "fix": "882", "desc": "883"}, {"messageId": "874", "data": "884", "fix": "885", "desc": "886"}, {"desc": "887", "fix": "888"}, {"messageId": "874", "data": "889", "fix": "890", "desc": "877"}, {"messageId": "874", "data": "891", "fix": "892", "desc": "880"}, {"messageId": "874", "data": "893", "fix": "894", "desc": "883"}, {"messageId": "874", "data": "895", "fix": "896", "desc": "886"}, {"messageId": "828", "fix": "897", "desc": "830"}, {"messageId": "831", "fix": "898", "desc": "833"}, {"messageId": "874", "data": "899", "fix": "900", "desc": "901"}, {"messageId": "874", "data": "902", "fix": "903", "desc": "904"}, {"messageId": "874", "data": "905", "fix": "906", "desc": "907"}, {"messageId": "874", "data": "908", "fix": "909", "desc": "910"}, {"messageId": "874", "data": "911", "fix": "912", "desc": "901"}, {"messageId": "874", "data": "913", "fix": "914", "desc": "904"}, {"messageId": "874", "data": "915", "fix": "916", "desc": "907"}, {"messageId": "874", "data": "917", "fix": "918", "desc": "910"}, {"messageId": "874", "data": "919", "fix": "920", "desc": "901"}, {"messageId": "874", "data": "921", "fix": "922", "desc": "904"}, {"messageId": "874", "data": "923", "fix": "924", "desc": "907"}, {"messageId": "874", "data": "925", "fix": "926", "desc": "910"}, {"messageId": "874", "data": "927", "fix": "928", "desc": "901"}, {"messageId": "874", "data": "929", "fix": "930", "desc": "904"}, {"messageId": "874", "data": "931", "fix": "932", "desc": "907"}, {"messageId": "874", "data": "933", "fix": "934", "desc": "910"}, {"messageId": "874", "data": "935", "fix": "936", "desc": "901"}, {"messageId": "874", "data": "937", "fix": "938", "desc": "904"}, {"messageId": "874", "data": "939", "fix": "940", "desc": "907"}, {"messageId": "874", "data": "941", "fix": "942", "desc": "910"}, {"messageId": "874", "data": "943", "fix": "944", "desc": "901"}, {"messageId": "874", "data": "945", "fix": "946", "desc": "904"}, {"messageId": "874", "data": "947", "fix": "948", "desc": "907"}, {"messageId": "874", "data": "949", "fix": "950", "desc": "910"}, {"messageId": "828", "fix": "951", "desc": "830"}, {"messageId": "831", "fix": "952", "desc": "833"}, {"messageId": "828", "fix": "953", "desc": "830"}, {"messageId": "831", "fix": "954", "desc": "833"}, {"messageId": "828", "fix": "955", "desc": "830"}, {"messageId": "831", "fix": "956", "desc": "833"}, {"messageId": "828", "fix": "957", "desc": "830"}, {"messageId": "831", "fix": "958", "desc": "833"}, {"messageId": "828", "fix": "959", "desc": "830"}, {"messageId": "831", "fix": "960", "desc": "833"}, {"messageId": "828", "fix": "961", "desc": "830"}, {"messageId": "831", "fix": "962", "desc": "833"}, {"messageId": "828", "fix": "963", "desc": "830"}, {"messageId": "831", "fix": "964", "desc": "833"}, {"messageId": "828", "fix": "965", "desc": "830"}, {"messageId": "831", "fix": "966", "desc": "833"}, {"messageId": "828", "fix": "967", "desc": "830"}, {"messageId": "831", "fix": "968", "desc": "833"}, {"messageId": "828", "fix": "969", "desc": "830"}, {"messageId": "831", "fix": "970", "desc": "833"}, {"messageId": "828", "fix": "971", "desc": "830"}, {"messageId": "831", "fix": "972", "desc": "833"}, {"messageId": "828", "fix": "973", "desc": "830"}, {"messageId": "831", "fix": "974", "desc": "833"}, {"messageId": "828", "fix": "975", "desc": "830"}, {"messageId": "831", "fix": "976", "desc": "833"}, {"messageId": "977", "fix": "978", "desc": "979"}, {"messageId": "828", "fix": "980", "desc": "830"}, {"messageId": "831", "fix": "981", "desc": "833"}, {"messageId": "828", "fix": "982", "desc": "830"}, {"messageId": "831", "fix": "983", "desc": "833"}, {"messageId": "828", "fix": "984", "desc": "830"}, {"messageId": "831", "fix": "985", "desc": "833"}, {"messageId": "828", "fix": "986", "desc": "830"}, {"messageId": "831", "fix": "987", "desc": "833"}, {"messageId": "828", "fix": "988", "desc": "830"}, {"messageId": "831", "fix": "989", "desc": "833"}, {"messageId": "828", "fix": "990", "desc": "830"}, {"messageId": "831", "fix": "991", "desc": "833"}, {"messageId": "828", "fix": "992", "desc": "830"}, {"messageId": "831", "fix": "993", "desc": "833"}, {"messageId": "828", "fix": "994", "desc": "830"}, {"messageId": "831", "fix": "995", "desc": "833"}, {"messageId": "828", "fix": "996", "desc": "830"}, {"messageId": "831", "fix": "997", "desc": "833"}, {"messageId": "828", "fix": "998", "desc": "830"}, {"messageId": "831", "fix": "999", "desc": "833"}, {"messageId": "828", "fix": "1000", "desc": "830"}, {"messageId": "831", "fix": "1001", "desc": "833"}, {"messageId": "828", "fix": "1002", "desc": "830"}, {"messageId": "831", "fix": "1003", "desc": "833"}, {"messageId": "828", "fix": "1004", "desc": "830"}, {"messageId": "831", "fix": "1005", "desc": "833"}, {"messageId": "828", "fix": "1006", "desc": "830"}, {"messageId": "831", "fix": "1007", "desc": "833"}, {"messageId": "828", "fix": "1008", "desc": "830"}, {"messageId": "831", "fix": "1009", "desc": "833"}, {"messageId": "828", "fix": "1010", "desc": "830"}, {"messageId": "831", "fix": "1011", "desc": "833"}, {"messageId": "828", "fix": "1012", "desc": "830"}, {"messageId": "831", "fix": "1013", "desc": "833"}, {"messageId": "828", "fix": "1014", "desc": "830"}, {"messageId": "831", "fix": "1015", "desc": "833"}, {"messageId": "828", "fix": "1016", "desc": "830"}, {"messageId": "831", "fix": "1017", "desc": "833"}, {"messageId": "828", "fix": "1018", "desc": "830"}, {"messageId": "831", "fix": "1019", "desc": "833"}, {"messageId": "828", "fix": "1020", "desc": "830"}, {"messageId": "831", "fix": "1021", "desc": "833"}, {"messageId": "828", "fix": "1022", "desc": "830"}, {"messageId": "831", "fix": "1023", "desc": "833"}, {"messageId": "828", "fix": "1024", "desc": "830"}, {"messageId": "831", "fix": "1025", "desc": "833"}, {"messageId": "828", "fix": "1026", "desc": "830"}, {"messageId": "831", "fix": "1027", "desc": "833"}, {"messageId": "828", "fix": "1028", "desc": "830"}, {"messageId": "831", "fix": "1029", "desc": "833"}, {"messageId": "828", "fix": "1030", "desc": "830"}, {"messageId": "831", "fix": "1031", "desc": "833"}, {"messageId": "828", "fix": "1032", "desc": "830"}, {"messageId": "831", "fix": "1033", "desc": "833"}, {"messageId": "828", "fix": "1034", "desc": "830"}, {"messageId": "831", "fix": "1035", "desc": "833"}, {"messageId": "828", "fix": "1036", "desc": "830"}, {"messageId": "831", "fix": "1037", "desc": "833"}, {"messageId": "828", "fix": "1038", "desc": "830"}, {"messageId": "831", "fix": "1039", "desc": "833"}, {"messageId": "828", "fix": "1040", "desc": "830"}, {"messageId": "831", "fix": "1041", "desc": "833"}, {"messageId": "828", "fix": "1042", "desc": "830"}, {"messageId": "831", "fix": "1043", "desc": "833"}, {"messageId": "828", "fix": "1044", "desc": "830"}, {"messageId": "831", "fix": "1045", "desc": "833"}, {"messageId": "828", "fix": "1046", "desc": "830"}, {"messageId": "831", "fix": "1047", "desc": "833"}, {"messageId": "828", "fix": "1048", "desc": "830"}, {"messageId": "831", "fix": "1049", "desc": "833"}, {"messageId": "828", "fix": "1050", "desc": "830"}, {"messageId": "831", "fix": "1051", "desc": "833"}, {"messageId": "828", "fix": "1052", "desc": "830"}, {"messageId": "831", "fix": "1053", "desc": "833"}, {"messageId": "828", "fix": "1054", "desc": "830"}, {"messageId": "831", "fix": "1055", "desc": "833"}, {"messageId": "828", "fix": "1056", "desc": "830"}, {"messageId": "831", "fix": "1057", "desc": "833"}, {"messageId": "828", "fix": "1058", "desc": "830"}, {"messageId": "831", "fix": "1059", "desc": "833"}, {"messageId": "828", "fix": "1060", "desc": "830"}, {"messageId": "831", "fix": "1061", "desc": "833"}, {"messageId": "828", "fix": "1062", "desc": "830"}, {"messageId": "831", "fix": "1063", "desc": "833"}, {"messageId": "828", "fix": "1064", "desc": "830"}, {"messageId": "831", "fix": "1065", "desc": "833"}, {"messageId": "828", "fix": "1066", "desc": "830"}, {"messageId": "831", "fix": "1067", "desc": "833"}, {"messageId": "828", "fix": "1068", "desc": "830"}, {"messageId": "831", "fix": "1069", "desc": "833"}, "suggestUnknown", {"range": "1070", "text": "1071"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "1072", "text": "1073"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "1074", "text": "1071"}, {"range": "1075", "text": "1073"}, {"range": "1076", "text": "1071"}, {"range": "1077", "text": "1073"}, {"range": "1078", "text": "1071"}, {"range": "1079", "text": "1073"}, {"range": "1080", "text": "1071"}, {"range": "1081", "text": "1073"}, {"range": "1082", "text": "1071"}, {"range": "1083", "text": "1073"}, {"range": "1084", "text": "1071"}, {"range": "1085", "text": "1073"}, {"range": "1086", "text": "1071"}, {"range": "1087", "text": "1073"}, {"range": "1088", "text": "1071"}, {"range": "1089", "text": "1073"}, {"range": "1090", "text": "1071"}, {"range": "1091", "text": "1073"}, {"range": "1092", "text": "1071"}, {"range": "1093", "text": "1073"}, {"range": "1094", "text": "1071"}, {"range": "1095", "text": "1073"}, {"range": "1096", "text": "1071"}, {"range": "1097", "text": "1073"}, {"range": "1098", "text": "1071"}, {"range": "1099", "text": "1073"}, "Update the dependencies array to be: [gameType, loadGameConfig]", {"range": "1100", "text": "1101"}, {"range": "1102", "text": "1071"}, {"range": "1103", "text": "1073"}, {"range": "1104", "text": "1071"}, {"range": "1105", "text": "1073"}, {"range": "1106", "text": "1071"}, {"range": "1107", "text": "1073"}, {"range": "1108", "text": "1071"}, {"range": "1109", "text": "1073"}, {"range": "1110", "text": "1071"}, {"range": "1111", "text": "1073"}, {"range": "1112", "text": "1071"}, {"range": "1113", "text": "1073"}, "replaceWithAlt", {"alt": "1114"}, {"range": "1115", "text": "1116"}, "Replace with `&apos;`.", {"alt": "1117"}, {"range": "1118", "text": "1119"}, "Replace with `&lsquo;`.", {"alt": "1120"}, {"range": "1121", "text": "1122"}, "Replace with `&#39;`.", {"alt": "1123"}, {"range": "1124", "text": "1125"}, "Replace with `&rsquo;`.", "Update the dependencies array to be: [loadLobbyData, user]", {"range": "1126", "text": "1127"}, {"alt": "1114"}, {"range": "1128", "text": "1129"}, {"alt": "1117"}, {"range": "1130", "text": "1131"}, {"alt": "1120"}, {"range": "1132", "text": "1133"}, {"alt": "1123"}, {"range": "1134", "text": "1135"}, {"range": "1136", "text": "1071"}, {"range": "1137", "text": "1073"}, {"alt": "1138"}, {"range": "1139", "text": "1140"}, "Replace with `&quot;`.", {"alt": "1141"}, {"range": "1142", "text": "1143"}, "Replace with `&ldquo;`.", {"alt": "1144"}, {"range": "1145", "text": "1146"}, "Replace with `&#34;`.", {"alt": "1147"}, {"range": "1148", "text": "1149"}, "Replace with `&rdquo;`.", {"alt": "1138"}, {"range": "1150", "text": "1151"}, {"alt": "1141"}, {"range": "1152", "text": "1153"}, {"alt": "1144"}, {"range": "1154", "text": "1155"}, {"alt": "1147"}, {"range": "1156", "text": "1157"}, {"alt": "1138"}, {"range": "1158", "text": "1159"}, {"alt": "1141"}, {"range": "1160", "text": "1161"}, {"alt": "1144"}, {"range": "1162", "text": "1163"}, {"alt": "1147"}, {"range": "1164", "text": "1165"}, {"alt": "1138"}, {"range": "1166", "text": "1167"}, {"alt": "1141"}, {"range": "1168", "text": "1169"}, {"alt": "1144"}, {"range": "1170", "text": "1171"}, {"alt": "1147"}, {"range": "1172", "text": "1173"}, {"alt": "1138"}, {"range": "1174", "text": "1175"}, {"alt": "1141"}, {"range": "1176", "text": "1177"}, {"alt": "1144"}, {"range": "1178", "text": "1179"}, {"alt": "1147"}, {"range": "1180", "text": "1181"}, {"alt": "1138"}, {"range": "1182", "text": "1183"}, {"alt": "1141"}, {"range": "1184", "text": "1185"}, {"alt": "1144"}, {"range": "1186", "text": "1187"}, {"alt": "1147"}, {"range": "1188", "text": "1189"}, {"range": "1190", "text": "1071"}, {"range": "1191", "text": "1073"}, {"range": "1192", "text": "1071"}, {"range": "1193", "text": "1073"}, {"range": "1194", "text": "1071"}, {"range": "1195", "text": "1073"}, {"range": "1196", "text": "1071"}, {"range": "1197", "text": "1073"}, {"range": "1198", "text": "1071"}, {"range": "1199", "text": "1073"}, {"range": "1200", "text": "1071"}, {"range": "1201", "text": "1073"}, {"range": "1202", "text": "1071"}, {"range": "1203", "text": "1073"}, {"range": "1204", "text": "1071"}, {"range": "1205", "text": "1073"}, {"range": "1206", "text": "1071"}, {"range": "1207", "text": "1073"}, {"range": "1208", "text": "1071"}, {"range": "1209", "text": "1073"}, {"range": "1210", "text": "1071"}, {"range": "1211", "text": "1073"}, {"range": "1212", "text": "1071"}, {"range": "1213", "text": "1073"}, {"range": "1214", "text": "1071"}, {"range": "1215", "text": "1073"}, "replaceEmptyInterfaceWithSuper", {"range": "1216", "text": "1217"}, "Replace empty interface with a type alias.", {"range": "1218", "text": "1071"}, {"range": "1219", "text": "1073"}, {"range": "1220", "text": "1071"}, {"range": "1221", "text": "1073"}, {"range": "1222", "text": "1071"}, {"range": "1223", "text": "1073"}, {"range": "1224", "text": "1071"}, {"range": "1225", "text": "1073"}, {"range": "1226", "text": "1071"}, {"range": "1227", "text": "1073"}, {"range": "1228", "text": "1071"}, {"range": "1229", "text": "1073"}, {"range": "1230", "text": "1071"}, {"range": "1231", "text": "1073"}, {"range": "1232", "text": "1071"}, {"range": "1233", "text": "1073"}, {"range": "1234", "text": "1071"}, {"range": "1235", "text": "1073"}, {"range": "1236", "text": "1071"}, {"range": "1237", "text": "1073"}, {"range": "1238", "text": "1071"}, {"range": "1239", "text": "1073"}, {"range": "1240", "text": "1071"}, {"range": "1241", "text": "1073"}, {"range": "1242", "text": "1071"}, {"range": "1243", "text": "1073"}, {"range": "1244", "text": "1071"}, {"range": "1245", "text": "1073"}, {"range": "1246", "text": "1071"}, {"range": "1247", "text": "1073"}, {"range": "1248", "text": "1071"}, {"range": "1249", "text": "1073"}, {"range": "1250", "text": "1071"}, {"range": "1251", "text": "1073"}, {"range": "1252", "text": "1071"}, {"range": "1253", "text": "1073"}, {"range": "1254", "text": "1071"}, {"range": "1255", "text": "1073"}, {"range": "1256", "text": "1071"}, {"range": "1257", "text": "1073"}, {"range": "1258", "text": "1071"}, {"range": "1259", "text": "1073"}, {"range": "1260", "text": "1071"}, {"range": "1261", "text": "1073"}, {"range": "1262", "text": "1071"}, {"range": "1263", "text": "1073"}, {"range": "1264", "text": "1071"}, {"range": "1265", "text": "1073"}, {"range": "1266", "text": "1071"}, {"range": "1267", "text": "1073"}, {"range": "1268", "text": "1071"}, {"range": "1269", "text": "1073"}, {"range": "1270", "text": "1071"}, {"range": "1271", "text": "1073"}, {"range": "1272", "text": "1071"}, {"range": "1273", "text": "1073"}, {"range": "1274", "text": "1071"}, {"range": "1275", "text": "1073"}, {"range": "1276", "text": "1071"}, {"range": "1277", "text": "1073"}, {"range": "1278", "text": "1071"}, {"range": "1279", "text": "1073"}, {"range": "1280", "text": "1071"}, {"range": "1281", "text": "1073"}, {"range": "1282", "text": "1071"}, {"range": "1283", "text": "1073"}, {"range": "1284", "text": "1071"}, {"range": "1285", "text": "1073"}, {"range": "1286", "text": "1071"}, {"range": "1287", "text": "1073"}, {"range": "1288", "text": "1071"}, {"range": "1289", "text": "1073"}, {"range": "1290", "text": "1071"}, {"range": "1291", "text": "1073"}, {"range": "1292", "text": "1071"}, {"range": "1293", "text": "1073"}, {"range": "1294", "text": "1071"}, {"range": "1295", "text": "1073"}, {"range": "1296", "text": "1071"}, {"range": "1297", "text": "1073"}, {"range": "1298", "text": "1071"}, {"range": "1299", "text": "1073"}, {"range": "1300", "text": "1071"}, {"range": "1301", "text": "1073"}, {"range": "1302", "text": "1071"}, {"range": "1303", "text": "1073"}, {"range": "1304", "text": "1071"}, {"range": "1305", "text": "1073"}, {"range": "1306", "text": "1071"}, {"range": "1307", "text": "1073"}, [860, 863], "unknown", [860, 863], "never", [644, 647], [644, 647], [2714, 2717], [2714, 2717], [3452, 3455], [3452, 3455], [3852, 3855], [3852, 3855], [5090, 5093], [5090, 5093], [1775, 1778], [1775, 1778], [868, 871], [868, 871], [1039, 1042], [1039, 1042], [1405, 1408], [1405, 1408], [1957, 1960], [1957, 1960], [2448, 2451], [2448, 2451], [2873, 2876], [2873, 2876], [2482, 2485], [2482, 2485], [3176, 3186], "[gameType, loadGameConfig]", [3434, 3437], [3434, 3437], [6786, 6789], [6786, 6789], [14402, 14405], [14402, 14405], [15144, 15147], [15144, 15147], [15795, 15798], [15795, 15798], [17491, 17494], [17491, 17494], "&apos;", [6863, 6956], "\n                Cash out anytime to secure your winnings before it&apos;s too late\n              ", "&lsquo;", [6863, 6956], "\n                Cash out anytime to secure your winnings before it&lsquo;s too late\n              ", "&#39;", [6863, 6956], "\n                Cash out anytime to secure your winnings before it&#39;s too late\n              ", "&rsquo;", [6863, 6956], "\n                Cash out anytime to secure your winnings before it&rsquo;s too late\n              ", [2398, 2404], "[loadL<PERSON><PERSON><PERSON><PERSON>, user]", [4942, 4981], "\n                Don&apos;t have an account?", [4942, 4981], "\n                Don&lsquo;t have an account?", [4942, 4981], "\n                Don&#39;t have an account?", [4942, 4981], "\n                Don&rsquo;t have an account?", [801, 804], [801, 804], "&quot;", [6806, 6843], "2. <PERSON><PERSON> &quot;Roll Under\" or \"Roll Over\"", "&ldquo;", [6806, 6843], "2. <PERSON><PERSON> &ldquo;Roll Under\" or \"Roll Over\"", "&#34;", [6806, 6843], "2. <PERSON><PERSON> &#34;Roll Under\" or \"Roll Over\"", "&rdquo;", [6806, 6843], "2. <PERSON><PERSON> &rdquo;Roll Under\" or \"Roll Over\"", [6806, 6843], "2. <PERSON><PERSON> \"Roll Under&quot; or \"Roll Over\"", [6806, 6843], "2. <PERSON><PERSON> \"Roll Under&ldquo; or \"Roll Over\"", [6806, 6843], "2. <PERSON><PERSON> \"Roll Under&#34; or \"Roll Over\"", [6806, 6843], "2. <PERSON><PERSON> \"Roll Under&rdquo; or \"Roll Over\"", [6806, 6843], "2. <PERSON><PERSON> \"Roll Under\" or &quot;Roll Over\"", [6806, 6843], "2. <PERSON><PERSON> \"Roll Under\" or &ldquo;Roll Over\"", [6806, 6843], "2. <PERSON><PERSON> \"Roll Under\" or &#34;Roll Over\"", [6806, 6843], "2. <PERSON><PERSON> \"Roll Under\" or &rdquo;Roll Over\"", [6806, 6843], "2. <PERSON><PERSON> \"Roll Under\" or \"Roll Over&quot;", [6806, 6843], "2. <PERSON><PERSON> \"Roll Under\" or \"Roll Over&ldquo;", [6806, 6843], "2. <PERSON><PERSON> \"Roll Under\" or \"Roll Over&#34;", [6806, 6843], "2. <PERSON><PERSON> \"Roll Under\" or \"Roll Over&rdquo;", [6865, 6904], "3. <PERSON><PERSON> &quot;<PERSON>\" to see if you win!", [6865, 6904], "3. <PERSON><PERSON> &ldq<PERSON>;<PERSON>\" to see if you win!", [6865, 6904], "3. <PERSON><PERSON> &#34;<PERSON>\" to see if you win!", [6865, 6904], "3. <PERSON><PERSON> &rd<PERSON><PERSON>;<PERSON>\" to see if you win!", [6865, 6904], "3. <PERSON><PERSON> \"Roll Dice&quot; to see if you win!", [6865, 6904], "3. <PERSON><PERSON> \"Roll Dice&ldquo; to see if you win!", [6865, 6904], "3. <PERSON><PERSON> \"Roll Dice&#34; to see if you win!", [6865, 6904], "3. <PERSON><PERSON> \"Roll Dice&rdquo; to see if you win!", [1083, 1086], [1083, 1086], [925, 928], [925, 928], [1517, 1520], [1517, 1520], [1585, 1588], [1585, 1588], [3989, 3992], [3989, 3992], [4067, 4070], [4067, 4070], [4161, 4164], [4161, 4164], [4201, 4204], [4201, 4204], [3866, 3869], [3866, 3869], [3930, 3933], [3930, 3933], [3989, 3992], [3989, 3992], [635, 638], [635, 638], [1499, 1502], [1499, 1502], [73, 150], "type InputProps = React.InputHTMLAttributes<HTMLInputElement>", [1103, 1106], [1103, 1106], [1902, 1905], [1902, 1905], [2051, 2054], [2051, 2054], [2131, 2134], [2131, 2134], [10477, 10480], [10477, 10480], [17031, 17034], [17031, 17034], [17418, 17421], [17418, 17421], [18806, 18809], [18806, 18809], [19081, 19084], [19081, 19084], [19345, 19348], [19345, 19348], [19495, 19498], [19495, 19498], [19750, 19753], [19750, 19753], [21556, 21559], [21556, 21559], [21879, 21882], [21879, 21882], [22093, 22096], [22093, 22096], [22308, 22311], [22308, 22311], [22944, 22947], [22944, 22947], [25208, 25211], [25208, 25211], [26002, 26005], [26002, 26005], [26164, 26167], [26164, 26167], [26524, 26527], [26524, 26527], [26706, 26709], [26706, 26709], [26958, 26961], [26958, 26961], [27673, 27676], [27673, 27676], [4447, 4450], [4447, 4450], [4822, 4825], [4822, 4825], [4898, 4901], [4898, 4901], [4955, 4958], [4955, 4958], [965, 968], [965, 968], [1540, 1543], [1540, 1543], [2172, 2175], [2172, 2175], [3757, 3760], [3757, 3760], [4051, 4054], [4051, 4054], [1897, 1900], [1897, 1900], [2306, 2309], [2306, 2309], [3435, 3438], [3435, 3438], [4420, 4423], [4420, 4423], [5224, 5227], [5224, 5227], [652, 655], [652, 655], [929, 932], [929, 932], [1096, 1099], [1096, 1099], [2402, 2405], [2402, 2405], [2412, 2415], [2412, 2415], [2743, 2746], [2743, 2746], [2753, 2756], [2753, 2756]]