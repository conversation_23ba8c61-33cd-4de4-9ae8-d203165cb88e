/**
 * Test the crash game provider directly
 */

const { GameFactory } = require('./lib/games/GameFactory');

async function testCrashProvider() {
  console.log('🧪 Testing Crash Game Provider...');
  
  try {
    // Initialize game factory
    const gameFactory = GameFactory.getInstance();
    await gameFactory.initialize();
    
    // Create a crash game
    const params = {
      userId: 1,
      betAmount: 10,
      clientSeed: 'test_client_seed'
    };
    
    console.log('📝 Input params:', params);
    
    const result = await gameFactory.createGame('crash', params);
    
    console.log('🎯 Game creation result:');
    console.log(JSON.stringify(result, null, 2));
    
    if (result.success && result.game) {
      console.log('\n🔍 Checking for crash point...');
      console.log('Crash point:', result.game.crash_point);
      console.log('Auto cash out:', result.game.auto_cash_out);
      console.log('Phase:', result.game.phase);
      console.log('Round ID:', result.game.round_id);
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

testCrashProvider();
