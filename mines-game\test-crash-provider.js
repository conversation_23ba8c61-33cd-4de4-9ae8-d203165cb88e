/**
 * Test the crash game provider directly
 */

// Since we can't import ES modules directly, let's test via API
const fetch = require('node-fetch');

let authCookie = '';

async function makeRequest(endpoint, options = {}) {
  const headers = {
    'Content-Type': 'application/json',
    ...options.headers
  };

  if (authCookie) {
    headers['Cookie'] = authCookie;
  }

  const response = await fetch(`http://localhost:3000${endpoint}`, {
    ...options,
    headers,
    credentials: 'include'
  });

  // Extract cookies from response
  const setCookieHeader = response.headers.get('set-cookie');
  if (setCookieHeader) {
    authCookie = setCookieHeader;
  }

  return response;
}

async function testCrashProvider() {
  console.log('🧪 Testing Crash Game Provider via API...');

  try {
    // First login
    console.log('🔐 Logging in...');
    const loginResponse = await makeRequest('/api/auth/login', {
      method: 'POST',
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'TestPass123'
      })
    });

    const loginResult = await loginResponse.json();
    if (!loginResult.success) {
      throw new Error('Login failed: ' + loginResult.error);
    }
    console.log('✅ Logged in successfully');

    // Test game creation via API
    console.log('🎮 Creating crash game...');
    const response = await makeRequest('/api/game/start', {
      method: 'POST',
      body: JSON.stringify({
        game_type: 'crash',
        bet_amount: 1
      })
    });

    const result = await response.json();

    console.log('🎯 API Response:');
    console.log(JSON.stringify(result, null, 2));

    if (result.success && result.game) {
      console.log('\n🔍 Checking for crash point...');
      console.log('Crash point:', result.game.crash_point);
      console.log('Auto cash out:', result.game.auto_cash_out);
      console.log('Phase:', result.game.phase);
      console.log('Round ID:', result.game.round_id);
      console.log('Game data keys:', Object.keys(result.game));
    }

  } catch (error) {
    console.error('❌ Error:', error);
  }
}

testCrashProvider();
