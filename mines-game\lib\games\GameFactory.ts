import { GameType, GameState, GameProviderInterface } from '@/types';
import { gameRegistry } from './registry';
import { MinesGameProvider } from './mines/MinesGameProvider';
import { DiceGameProvider } from './dice/DiceGameProvider';
import { CrashGameProvider } from './crash/CrashGameProvider';
import { createPlaceholderProviders } from './PlaceholderGameProvider';

/**
 * Game Factory - Creates and manages game instances
 */
export class GameFactory {
  private static instance: GameFactory;
  private initialized = false;

  private constructor() {}

  /**
   * Get singleton instance
   */
  public static getInstance(): GameFactory {
    if (!GameFactory.instance) {
      GameFactory.instance = new GameFactory();
    }
    return GameFactory.instance;
  }

  /**
   * Initialize all game providers
   */
  public async initialize(): Promise<void> {
    if (this.initialized) {
      console.log('🎮 Game factory already initialized');
      return;
    }

    console.log('🎮 Initializing game factory...');

    try {
      // Register all game providers
      await this.registerAllGames();

      this.initialized = true;
      console.log('✅ Game factory initialized successfully');

      // Log registry stats
      const stats = gameRegistry.getRegistryStats();
      console.log(`📊 Registered ${stats.totalGames} games (${stats.activeGames} active)`);
      console.log('📋 Games by category:', stats.gamesByCategory);

    } catch (error) {
      console.error('❌ Failed to initialize game factory:', error);
      throw error;
    }
  }

  /**
   * Register all available game providers
   */
  private async registerAllGames(): Promise<void> {
    const providers = [
      new MinesGameProvider(),
      new DiceGameProvider(),
      new CrashGameProvider(),
      ...createPlaceholderProviders(), // Add placeholder games for lobby display
    ];

    for (const provider of providers) {
      try {
        gameRegistry.registerGame(provider.config, provider);
      } catch (error) {
        console.error(`Failed to register ${provider.gameType} game:`, error);
        // Continue with other games even if one fails
      }
    }
  }

  /**
   * Create a new game instance
   */
  public async createGame<T extends GameState = GameState>(
    gameType: GameType,
    params: any
  ): Promise<{ success: boolean; game?: Partial<T>; error?: string }> {
    try {
      if (!this.initialized) {
        await this.initialize();
      }

      const provider = gameRegistry.getGameProvider(gameType);
      if (!provider) {
        return {
          success: false,
          error: `Game type '${gameType}' is not registered`
        };
      }

      // Validate parameters
      if (!provider.validateGameParams(params)) {
        return {
          success: false,
          error: 'Invalid game parameters'
        };
      }

      // Generate game data
      const gameData = provider.generateGameData(params) as Partial<T>;

      return {
        success: true,
        game: gameData
      };

    } catch (error) {
      console.error(`Error creating ${gameType} game:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Process a game action
   */
  public async processGameAction<T extends GameState = GameState>(
    gameType: GameType,
    gameState: T,
    actionType: string,
    payload?: any
  ): Promise<{ success: boolean; gameState?: T; error?: string }> {
    try {
      if (!this.initialized) {
        await this.initialize();
      }

      const provider = gameRegistry.getGameProvider(gameType);
      if (!provider) {
        return {
          success: false,
          error: `Game type '${gameType}' is not registered`
        };
      }

      const action = { type: actionType, payload };
      const updatedGameState = await provider.processGameAction(gameState, action) as T;

      return {
        success: true,
        gameState: updatedGameState
      };

    } catch (error) {
      console.error(`Error processing ${gameType} action:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Calculate multiplier for a game state
   */
  public calculateMultiplier<T extends GameState = GameState>(
    gameType: GameType,
    gameState: T,
    params?: any
  ): number {
    if (!this.initialized) {
      console.warn('Game factory not initialized, returning default multiplier');
      return 1.0;
    }

    const provider = gameRegistry.getGameProvider(gameType);
    if (!provider) {
      console.warn(`Game type '${gameType}' not found, returning default multiplier`);
      return 1.0;
    }

    return provider.calculateMultiplier(gameState, params);
  }

  /**
   * Get game configuration
   */
  public getGameConfig(gameType: GameType) {
    return gameRegistry.getGameConfig(gameType);
  }

  /**
   * Get all available games
   */
  public getAllGames() {
    return gameRegistry.getAllGames();
  }

  /**
   * Get games by category
   */
  public getGamesByCategory(category: string) {
    return gameRegistry.getGamesByCategory(category as any);
  }

  /**
   * Search games
   */
  public searchGames(query: string) {
    return gameRegistry.searchGames(query);
  }

  /**
   * Check if game type is available
   */
  public isGameAvailable(gameType: GameType): boolean {
    const config = gameRegistry.getGameConfig(gameType);
    return config?.isActive ?? false;
  }

  /**
   * Get game provider (for advanced usage)
   */
  public getGameProvider(gameType: GameType): GameProviderInterface | undefined {
    return gameRegistry.getGameProvider(gameType);
  }

  /**
   * Reset factory (for testing)
   */
  public reset(): void {
    gameRegistry.clear();
    this.initialized = false;
    console.log('🔄 Game factory reset');
  }

  /**
   * Get initialization status
   */
  public isInitialized(): boolean {
    return this.initialized;
  }
}

// Export singleton instance
export const gameFactory = GameFactory.getInstance();

// Auto-initialize in production
if (process.env.NODE_ENV === 'production') {
  gameFactory.initialize().catch(console.error);
}
