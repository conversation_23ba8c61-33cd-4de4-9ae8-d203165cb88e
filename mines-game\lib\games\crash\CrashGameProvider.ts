import { CrashGameState, GameAction, GameConfig } from '@/types';
import { BaseGameProvider, GameActionType } from '../BaseGameProvider';

/**
 * Crash Game Provider - Handles crash game logic
 */
export class CrashGameProvider extends BaseGameProvider<CrashGameState> {
  public readonly gameType = 'crash' as const;
  
  public readonly config: GameConfig = {
    id: 'crash',
    name: 'Crash',
    description: 'Watch the multiplier rise and cash out before it crashes! The longer you wait, the higher the multiplier, but if you wait too long, you lose everything.',
    icon: '🚀',
    category: 'originals',
    minBet: 0.01,
    maxBet: 1000,
    houseEdge: 0.01, // 1%
    maxMultiplier: 1000000, // 1,000,000x theoretical max
    features: ['Provably Fair', 'Real-time', 'Auto Cash Out', 'Live Multiplier'],
    isActive: true,
    isNew: true,
    isFeatured: true
  };

  /**
   * Validate crash game parameters
   */
  public validateGameParams(params: any): boolean {
    const { bet_amount, auto_cash_out } = params;
    
    if (!this.validateBaseParams(bet_amount)) {
      return false;
    }

    // Validate auto cash out if provided
    if (auto_cash_out !== undefined) {
      if (typeof auto_cash_out !== 'number' || auto_cash_out < 1.01) {
        return false;
      }
      if (auto_cash_out > this.config.maxMultiplier) {
        return false;
      }
    }

    return true;
  }

  /**
   * Calculate current multiplier based on time elapsed
   */
  public calculateMultiplier(gameState: CrashGameState, params?: any): number {
    if (gameState.phase !== 'flying') {
      return 1.0;
    }

    const timeElapsed = gameState.time_elapsed || 0;
    
    // Use exponential growth: multiplier = 1.002^(time_in_ms)
    // This creates a smooth curve that accelerates over time
    const multiplier = Math.pow(1.002, timeElapsed);
    
    // Cap at crash point if it exists
    if (gameState.crash_point && multiplier >= gameState.crash_point) {
      return gameState.crash_point;
    }

    return Math.round(multiplier * 100) / 100; // Round to 2 decimal places
  }

  /**
   * Generate crash game data
   */
  public generateGameData(params: any): Partial<CrashGameState> {
    const { bet_amount, auto_cash_out, user_id, client_seed } = params;
    
    const baseData = this.generateBaseGameData(user_id, bet_amount, client_seed);
    
    // Generate crash point using provably fair method
    const crashPoint = this.generateCrashPoint(baseData.server_seed!, baseData.client_seed!);
    
    return {
      ...baseData,
      game_type: 'crash',
      crash_point: crashPoint,
      auto_cash_out: auto_cash_out,
      phase: 'betting',
      time_elapsed: 0,
      cashed_out: false,
      round_id: this.generateRoundId()
    };
  }

  /**
   * Process crash game actions
   */
  public async processGameAction(gameState: CrashGameState, action: GameAction): Promise<CrashGameState> {
    this.logGameAction(gameState, action);

    switch (action.type) {
      case GameActionType.START_GAME:
        return this.handleStartGame(gameState, action.payload);
      
      case GameActionType.CASH_OUT:
        return this.handleCashOut(gameState, action.payload);
      
      case 'UPDATE_MULTIPLIER':
        return this.handleUpdateMultiplier(gameState, action.payload);
      
      case 'CRASH':
        return this.handleCrash(gameState);
      
      case 'START_ROUND':
        return this.handleStartRound(gameState);
      
      case 'END_ROUND':
        return this.handleEndRound(gameState);
      
      default:
        throw new Error(`Unknown action type: ${action.type}`);
    }
  }

  /**
   * Handle starting the game (betting phase)
   */
  private handleStartGame(gameState: CrashGameState, payload: any): CrashGameState {
    return {
      ...gameState,
      phase: 'betting',
      status: 'active',
      time_elapsed: 0,
      current_multiplier: 1.0,
      cashed_out: false
    };
  }

  /**
   * Handle cash out action
   */
  private handleCashOut(gameState: CrashGameState, payload: any): CrashGameState {
    if (gameState.phase !== 'flying' || gameState.cashed_out) {
      throw new Error('Cannot cash out at this time');
    }

    const currentMultiplier = this.calculateMultiplier(gameState);
    const profit = this.calculateProfit(gameState.bet_amount, currentMultiplier);

    return {
      ...gameState,
      cashed_out: true,
      cash_out_at: currentMultiplier,
      current_multiplier: currentMultiplier,
      profit: profit,
      status: 'cashed_out'
    };
  }

  /**
   * Handle multiplier update during flight
   */
  private handleUpdateMultiplier(gameState: CrashGameState, payload: { timeElapsed: number }): CrashGameState {
    if (gameState.phase !== 'flying') {
      return gameState;
    }

    const newMultiplier = this.calculateMultiplier({ ...gameState, time_elapsed: payload.timeElapsed });
    
    // Check if we've hit the crash point
    if (gameState.crash_point && newMultiplier >= gameState.crash_point) {
      return this.handleCrash({
        ...gameState,
        time_elapsed: payload.timeElapsed,
        current_multiplier: gameState.crash_point
      });
    }

    // Check for auto cash out
    if (gameState.auto_cash_out && newMultiplier >= gameState.auto_cash_out && !gameState.cashed_out) {
      return this.handleCashOut({
        ...gameState,
        time_elapsed: payload.timeElapsed,
        current_multiplier: newMultiplier
      }, {});
    }

    return {
      ...gameState,
      time_elapsed: payload.timeElapsed,
      current_multiplier: newMultiplier
    };
  }

  /**
   * Handle crash event
   */
  private handleCrash(gameState: CrashGameState): CrashGameState {
    const profit = gameState.cashed_out ? gameState.profit : -gameState.bet_amount;
    
    return {
      ...gameState,
      phase: 'crashed',
      status: gameState.cashed_out ? 'cashed_out' : 'lost',
      profit: profit,
      current_multiplier: gameState.crash_point || gameState.current_multiplier
    };
  }

  /**
   * Handle start of flying phase
   */
  private handleStartRound(gameState: CrashGameState): CrashGameState {
    return {
      ...gameState,
      phase: 'flying',
      time_elapsed: 0,
      current_multiplier: 1.0
    };
  }

  /**
   * Handle end of round (waiting phase)
   */
  private handleEndRound(gameState: CrashGameState): CrashGameState {
    return {
      ...gameState,
      phase: 'waiting'
    };
  }

  /**
   * Generate crash point using provably fair method
   */
  private generateCrashPoint(serverSeed: string, clientSeed: string): number {
    // Generate a random number between 0 and 1
    const random = this.generateRandomFromSeeds(serverSeed, clientSeed, 0);
    
    // Use inverse exponential distribution to generate crash point
    // This creates realistic crash points with most being low but some being very high
    const houseEdge = this.config.houseEdge;
    const crashPoint = Math.max(1.01, (1 - houseEdge) / random);
    
    // Cap at maximum multiplier
    return Math.min(crashPoint, this.config.maxMultiplier);
  }

  /**
   * Generate unique round ID
   */
  private generateRoundId(): string {
    return `crash_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
