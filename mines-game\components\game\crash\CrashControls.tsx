import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { User, CrashGameState } from '@/types';
import { Rocket, DollarSign, Timer, TrendingUp } from 'lucide-react';

interface CrashControlsProps {
  user: User | null;
  gameState: CrashGameState | null;
  onStartGame: (betAmount: number, autoCashOut?: number) => Promise<void>;
  onCashOut: () => Promise<void>;
  loading: boolean;
  canPlaceBet: boolean;
  canCashOut: boolean;
  currentMultiplier: number;
  roundPhase: 'betting' | 'flying' | 'crashed' | 'waiting';
  timeUntilNextRound: number;
  getCrashStats: () => any;
}

export function CrashControls({
  user,
  gameState,
  onStartGame,
  onCashOut,
  loading,
  canPlaceBet,
  canCashOut,
  currentMultiplier,
  roundPhase,
  timeUntilNextRound,
  getCrashStats
}: CrashControlsProps) {
  const [betAmount, setBetAmount] = useState(1);
  const [autoCashOut, setAutoCashOut] = useState<number | undefined>(undefined);
  const [countdown, setCountdown] = useState(0);

  const stats = getCrashStats();

  // Countdown timer for betting phase
  useEffect(() => {
    if (roundPhase === 'betting' && timeUntilNextRound > 0) {
      setCountdown(Math.ceil(timeUntilNextRound / 1000));
      const timer = setInterval(() => {
        setCountdown(prev => Math.max(0, prev - 1));
      }, 1000);
      return () => clearInterval(timer);
    } else if (roundPhase === 'waiting' && timeUntilNextRound > 0) {
      setCountdown(Math.ceil(timeUntilNextRound / 1000));
      const timer = setInterval(() => {
        setCountdown(prev => Math.max(0, prev - 1));
      }, 1000);
      return () => clearInterval(timer);
    }
  }, [roundPhase, timeUntilNextRound]);

  const handleStartGame = async () => {
    if (!canPlaceBet || betAmount <= 0) return;
    
    try {
      await onStartGame(betAmount, autoCashOut);
    } catch (error) {
      console.error('Failed to start crash game:', error);
    }
  };

  const handleCashOut = async () => {
    if (!canCashOut) return;
    
    try {
      await onCashOut();
    } catch (error) {
      console.error('Failed to cash out:', error);
    }
  };

  const getPhaseDisplay = () => {
    switch (roundPhase) {
      case 'betting':
        return {
          title: 'Place Your Bet',
          subtitle: `Round starts in ${countdown}s`,
          color: 'bg-blue-500'
        };
      case 'flying':
        return {
          title: 'Flying!',
          subtitle: 'Cash out before it crashes!',
          color: 'bg-green-500'
        };
      case 'crashed':
        return {
          title: 'Crashed!',
          subtitle: `Next round in ${countdown}s`,
          color: 'bg-red-500'
        };
      case 'waiting':
        return {
          title: 'Waiting',
          subtitle: `Next round in ${countdown}s`,
          color: 'bg-gray-500'
        };
      default:
        return {
          title: 'Crash',
          subtitle: 'Waiting for round...',
          color: 'bg-gray-500'
        };
    }
  };

  const phaseDisplay = getPhaseDisplay();

  return (
    <div className="space-y-4">
      {/* Phase Indicator */}
      <Card className="bg-gray-800/80 border-gray-600">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className={`w-3 h-3 rounded-full ${phaseDisplay.color} animate-pulse`} />
              <div>
                <h3 className="text-white font-semibold">{phaseDisplay.title}</h3>
                <p className="text-gray-400 text-sm">{phaseDisplay.subtitle}</p>
              </div>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-white">
                {currentMultiplier.toFixed(2)}x
              </div>
              <div className="text-sm text-gray-400">Multiplier</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Betting Controls */}
      {roundPhase === 'betting' && !gameState && (
        <Card className="bg-gray-800/80 border-gray-600">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <Rocket className="h-5 w-5 mr-2" />
              Place Bet
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Bet Amount */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Bet Amount
              </label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  type="number"
                  value={betAmount}
                  onChange={(e) => setBetAmount(Number(e.target.value))}
                  min="0.01"
                  max={user?.usdt_balance || 1000}
                  step="0.01"
                  className="pl-10 bg-gray-700 border-gray-600 text-white"
                  placeholder="Enter bet amount"
                />
              </div>
              <div className="flex justify-between mt-2">
                <span className="text-xs text-gray-400">
                  Min: $0.01
                </span>
                <span className="text-xs text-gray-400">
                  Balance: ${user?.usdt_balance?.toFixed(2) || '0.00'}
                </span>
              </div>
            </div>

            {/* Auto Cash Out */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Auto Cash Out (Optional)
              </label>
              <div className="relative">
                <TrendingUp className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  type="number"
                  value={autoCashOut || ''}
                  onChange={(e) => setAutoCashOut(e.target.value ? Number(e.target.value) : undefined)}
                  min="1.01"
                  step="0.01"
                  className="pl-10 bg-gray-700 border-gray-600 text-white"
                  placeholder="e.g., 2.00"
                />
              </div>
              <p className="text-xs text-gray-400 mt-1">
                Automatically cash out at this multiplier
              </p>
            </div>

            {/* Quick Bet Buttons */}
            <div className="grid grid-cols-4 gap-2">
              {[0.1, 1, 10, 100].map((amount) => (
                <Button
                  key={amount}
                  variant="outline"
                  size="sm"
                  onClick={() => setBetAmount(amount)}
                  className="border-gray-600 text-gray-300 hover:bg-gray-700"
                >
                  ${amount}
                </Button>
              ))}
            </div>

            {/* Place Bet Button */}
            <Button
              onClick={handleStartGame}
              disabled={loading || !canPlaceBet || betAmount <= 0 || countdown <= 0}
              className="w-full bg-green-600 hover:bg-green-700 text-white"
            >
              {loading ? 'Placing Bet...' : `Place Bet ($${betAmount})`}
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Cash Out Button */}
      {gameState && roundPhase === 'flying' && (
        <Card className="bg-gray-800/80 border-gray-600">
          <CardContent className="p-4">
            <div className="text-center space-y-4">
              <div>
                <div className="text-3xl font-bold text-green-400">
                  ${(gameState.bet_amount * currentMultiplier).toFixed(2)}
                </div>
                <div className="text-sm text-gray-400">
                  Potential Payout
                </div>
              </div>
              
              <Button
                onClick={handleCashOut}
                disabled={loading || !canCashOut}
                className="w-full bg-orange-600 hover:bg-orange-700 text-white text-lg py-3"
              >
                {loading ? 'Cashing Out...' : `Cash Out @ ${currentMultiplier.toFixed(2)}x`}
              </Button>
              
              {gameState.auto_cash_out && (
                <div className="text-xs text-gray-400">
                  Auto cash out at {gameState.auto_cash_out}x
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Game Stats */}
      {stats && (
        <Card className="bg-gray-800/80 border-gray-600">
          <CardHeader>
            <CardTitle className="text-white text-sm">Current Round</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">Bet Amount:</span>
              <span className="text-white">${stats.betAmount}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">Current Multiplier:</span>
              <span className="text-green-400">{stats.currentMultiplier.toFixed(2)}x</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">Potential Payout:</span>
              <span className="text-white">${stats.potentialPayout.toFixed(2)}</span>
            </div>
            {stats.autoCashOut && (
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Auto Cash Out:</span>
                <span className="text-yellow-400">{stats.autoCashOut}x</span>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
