"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./lib/games/GameFactory.ts":
/*!**********************************!*\
  !*** ./lib/games/GameFactory.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GameFactory: () => (/* binding */ GameFactory),\n/* harmony export */   gameFactory: () => (/* binding */ gameFactory)\n/* harmony export */ });\n/* harmony import */ var _registry__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./registry */ \"(pages-dir-browser)/./lib/games/registry.ts\");\n/* harmony import */ var _mines_MinesGameProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mines/MinesGameProvider */ \"(pages-dir-browser)/./lib/games/mines/MinesGameProvider.ts\");\n/* harmony import */ var _dice_DiceGameProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./dice/DiceGameProvider */ \"(pages-dir-browser)/./lib/games/dice/DiceGameProvider.ts\");\n/* harmony import */ var _crash_CrashGameProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./crash/CrashGameProvider */ \"(pages-dir-browser)/./lib/games/crash/CrashGameProvider.ts\");\n/* harmony import */ var _PlaceholderGameProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./PlaceholderGameProvider */ \"(pages-dir-browser)/./lib/games/PlaceholderGameProvider.ts\");\n\n\n\n\n\n/**\n * Game Factory - Creates and manages game instances\n */ class GameFactory {\n    /**\n   * Get singleton instance\n   */ static getInstance() {\n        if (!GameFactory.instance) {\n            GameFactory.instance = new GameFactory();\n        }\n        return GameFactory.instance;\n    }\n    /**\n   * Initialize all game providers\n   */ async initialize() {\n        if (this.initialized) {\n            console.log('🎮 Game factory already initialized');\n            return;\n        }\n        console.log('🎮 Initializing game factory...');\n        try {\n            // Register all game providers\n            await this.registerAllGames();\n            this.initialized = true;\n            console.log('✅ Game factory initialized successfully');\n            // Log registry stats\n            const stats = _registry__WEBPACK_IMPORTED_MODULE_0__.gameRegistry.getRegistryStats();\n            console.log(\"\\uD83D\\uDCCA Registered \".concat(stats.totalGames, \" games (\").concat(stats.activeGames, \" active)\"));\n            console.log('📋 Games by category:', stats.gamesByCategory);\n        } catch (error) {\n            console.error('❌ Failed to initialize game factory:', error);\n            throw error;\n        }\n    }\n    /**\n   * Register all available game providers\n   */ async registerAllGames() {\n        const providers = [\n            new _mines_MinesGameProvider__WEBPACK_IMPORTED_MODULE_1__.MinesGameProvider(),\n            new _dice_DiceGameProvider__WEBPACK_IMPORTED_MODULE_2__.DiceGameProvider(),\n            new _crash_CrashGameProvider__WEBPACK_IMPORTED_MODULE_3__.CrashGameProvider(),\n            ...(0,_PlaceholderGameProvider__WEBPACK_IMPORTED_MODULE_4__.createPlaceholderProviders)()\n        ];\n        for (const provider of providers){\n            try {\n                _registry__WEBPACK_IMPORTED_MODULE_0__.gameRegistry.registerGame(provider.config, provider);\n            } catch (error) {\n                console.error(\"Failed to register \".concat(provider.gameType, \" game:\"), error);\n            // Continue with other games even if one fails\n            }\n        }\n    }\n    /**\n   * Create a new game instance\n   */ async createGame(gameType, params) {\n        try {\n            if (!this.initialized) {\n                await this.initialize();\n            }\n            const provider = _registry__WEBPACK_IMPORTED_MODULE_0__.gameRegistry.getGameProvider(gameType);\n            console.log(\"\\uD83C\\uDFAE GameFactory - Retrieved provider for \".concat(gameType, \":\"), provider === null || provider === void 0 ? void 0 : provider.constructor.name);\n            if (!provider) {\n                return {\n                    success: false,\n                    error: \"Game type '\".concat(gameType, \"' is not registered\")\n                };\n            }\n            // Validate parameters\n            console.log(\"\\uD83C\\uDFAE GameFactory - Validating params for \".concat(gameType, \":\"), params);\n            if (!provider.validateGameParams(params)) {\n                return {\n                    success: false,\n                    error: 'Invalid game parameters'\n                };\n            }\n            // Generate game data\n            console.log(\"\\uD83C\\uDFAE GameFactory - Calling generateGameData for \".concat(gameType));\n            const gameData = provider.generateGameData(params);\n            console.log(\"\\uD83C\\uDFAE GameFactory - Generated game data:\", gameData);\n            return {\n                success: true,\n                game: gameData\n            };\n        } catch (error) {\n            console.error(\"Error creating \".concat(gameType, \" game:\"), error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n    /**\n   * Process a game action\n   */ async processGameAction(gameType, gameState, actionType, payload) {\n        try {\n            if (!this.initialized) {\n                await this.initialize();\n            }\n            const provider = _registry__WEBPACK_IMPORTED_MODULE_0__.gameRegistry.getGameProvider(gameType);\n            if (!provider) {\n                return {\n                    success: false,\n                    error: \"Game type '\".concat(gameType, \"' is not registered\")\n                };\n            }\n            const action = {\n                type: actionType,\n                payload\n            };\n            const updatedGameState = await provider.processGameAction(gameState, action);\n            return {\n                success: true,\n                gameState: updatedGameState\n            };\n        } catch (error) {\n            console.error(\"Error processing \".concat(gameType, \" action:\"), error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n    /**\n   * Calculate multiplier for a game state\n   */ calculateMultiplier(gameType, gameState, params) {\n        if (!this.initialized) {\n            console.warn('Game factory not initialized, returning default multiplier');\n            return 1.0;\n        }\n        const provider = _registry__WEBPACK_IMPORTED_MODULE_0__.gameRegistry.getGameProvider(gameType);\n        if (!provider) {\n            console.warn(\"Game type '\".concat(gameType, \"' not found, returning default multiplier\"));\n            return 1.0;\n        }\n        return provider.calculateMultiplier(gameState, params);\n    }\n    /**\n   * Get game configuration\n   */ getGameConfig(gameType) {\n        return _registry__WEBPACK_IMPORTED_MODULE_0__.gameRegistry.getGameConfig(gameType);\n    }\n    /**\n   * Get all available games\n   */ getAllGames() {\n        return _registry__WEBPACK_IMPORTED_MODULE_0__.gameRegistry.getAllGames();\n    }\n    /**\n   * Get games by category\n   */ getGamesByCategory(category) {\n        return _registry__WEBPACK_IMPORTED_MODULE_0__.gameRegistry.getGamesByCategory(category);\n    }\n    /**\n   * Search games\n   */ searchGames(query) {\n        return _registry__WEBPACK_IMPORTED_MODULE_0__.gameRegistry.searchGames(query);\n    }\n    /**\n   * Check if game type is available\n   */ isGameAvailable(gameType) {\n        const config = _registry__WEBPACK_IMPORTED_MODULE_0__.gameRegistry.getGameConfig(gameType);\n        var _config_isActive;\n        return (_config_isActive = config === null || config === void 0 ? void 0 : config.isActive) !== null && _config_isActive !== void 0 ? _config_isActive : false;\n    }\n    /**\n   * Get game provider (for advanced usage)\n   */ getGameProvider(gameType) {\n        return _registry__WEBPACK_IMPORTED_MODULE_0__.gameRegistry.getGameProvider(gameType);\n    }\n    /**\n   * Reset factory (for testing)\n   */ reset() {\n        _registry__WEBPACK_IMPORTED_MODULE_0__.gameRegistry.clear();\n        this.initialized = false;\n        console.log('🔄 Game factory reset');\n    }\n    /**\n   * Get initialization status\n   */ isInitialized() {\n        return this.initialized;\n    }\n    constructor(){\n        this.initialized = false;\n    }\n}\n// Export singleton instance\nconst gameFactory = GameFactory.getInstance();\n// Auto-initialize in production\nif (false) {}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./lib/games/GameFactory.ts\n"));

/***/ })

});