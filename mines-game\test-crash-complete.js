/**
 * Complete Crash Game Testing Script
 * Tests all crash game functionality including rocket movement and crash mechanics
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';
let authCookie = '';

// Test user credentials
const testUser = {
  username: 'crashtest2',
  email: '<EMAIL>',
  password: 'TestPass123'
};

/**
 * Helper function to make authenticated requests
 */
async function makeRequest(endpoint, options = {}) {
  const url = `${BASE_URL}${endpoint}`;
  const headers = {
    'Content-Type': 'application/json',
    ...options.headers
  };
  
  if (authCookie) {
    headers['Cookie'] = authCookie;
  }

  const response = await fetch(url, {
    ...options,
    headers,
    credentials: 'include'
  });

  // Extract cookies from response
  const setCookieHeader = response.headers.get('set-cookie');
  if (setCookieHeader) {
    authCookie = setCookieHeader;
  }

  return response;
}

/**
 * Create test account and add balance
 */
async function setupTestAccount() {
  console.log('🔧 Setting up test account...');
  
  // Create account
  const signupResponse = await makeRequest('/api/auth/signup', {
    method: 'POST',
    body: JSON.stringify(testUser)
  });

  const signupData = await signupResponse.json();
  if (!signupData.success) {
    console.log('ℹ️  Account might already exist, trying to login...');
    
    // Try to login instead
    const loginResponse = await makeRequest('/api/auth/login', {
      method: 'POST',
      body: JSON.stringify({
        email: testUser.email,
        password: testUser.password
      })
    });
    
    const loginData = await loginResponse.json();
    if (!loginData.success) {
      throw new Error('Failed to login: ' + loginData.error);
    }
  }

  // Add test balance
  const balanceResponse = await makeRequest('/api/test/add-balance', {
    method: 'POST',
    body: JSON.stringify({ amount: 100 })
  });

  const balanceData = await balanceResponse.json();
  if (!balanceData.success) {
    throw new Error('Failed to add balance: ' + balanceData.error);
  }

  console.log('✅ Test account ready with $100 balance');
  return true;
}

/**
 * Test placing a bet
 */
async function testPlaceBet(betAmount = 5) {
  console.log(`\n🎯 Testing bet placement ($${betAmount})...`);
  
  const response = await makeRequest('/api/game/start', {
    method: 'POST',
    body: JSON.stringify({
      game_type: 'crash',
      bet_amount: betAmount
    })
  });

  const data = await response.json();
  
  if (data.success) {
    console.log('✅ Bet placed successfully!');
    console.log(`   Game ID: ${data.game?.id || 'N/A'}`);
    console.log(`   Bet Amount: $${data.game?.bet_amount || betAmount}`);
    console.log(`   Crash Point: ${data.game?.crash_point?.toFixed(2) || 'Hidden'}x`);
    return data.game || data.gameState;
  } else {
    console.log('❌ Failed to place bet:', data.error);
    return null;
  }
}

/**
 * Simulate watching the rocket fly and cash out at specific multiplier
 */
async function testRocketFlight(gameState, targetMultiplier = 2.0) {
  console.log(`\n🚀 Watching rocket flight (target cash out: ${targetMultiplier}x)...`);
  
  const startTime = Date.now();
  let currentMultiplier = 1.0;
  let cashedOut = false;
  
  // Simulate watching the rocket for up to 30 seconds
  const maxWatchTime = 30000;
  
  while (Date.now() - startTime < maxWatchTime && !cashedOut) {
    // Calculate current multiplier based on time elapsed
    const timeElapsed = Date.now() - startTime;
    currentMultiplier = Math.pow(1.002, timeElapsed);
    
    // Display current status
    process.stdout.write(`\r🚀 Flying: ${currentMultiplier.toFixed(2)}x (${(timeElapsed/1000).toFixed(1)}s)`);
    
    // Try to cash out when we reach target multiplier
    if (currentMultiplier >= targetMultiplier && !cashedOut) {
      console.log(`\n💰 Attempting to cash out at ${currentMultiplier.toFixed(2)}x...`);
      
      const cashOutResult = await testCashOut(gameState);
      if (cashOutResult) {
        cashedOut = true;
        return {
          success: true,
          cashedOut: true,
          multiplier: currentMultiplier,
          profit: cashOutResult.profit
        };
      }
    }
    
    // Small delay to simulate real-time watching
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  if (!cashedOut) {
    console.log(`\n💥 Rocket likely crashed or test timed out at ${currentMultiplier.toFixed(2)}x`);
    return {
      success: true,
      cashedOut: false,
      multiplier: currentMultiplier,
      crashed: true
    };
  }
}

/**
 * Test cash out functionality
 */
async function testCashOut(gameState) {
  const response = await makeRequest('/api/game/cashout', {
    method: 'POST',
    body: JSON.stringify({
      game_id: gameState?.id,
      game_type: 'crash'
    })
  });

  const data = await response.json();
  
  if (data.success) {
    console.log('✅ Cash out successful!');
    console.log(`   Profit: $${data.profit?.toFixed(2) || 'N/A'}`);
    console.log(`   Multiplier: ${data.multiplier?.toFixed(2) || 'N/A'}x`);
    return data;
  } else {
    console.log('❌ Cash out failed:', data.error);
    return null;
  }
}

/**
 * Test auto cash out functionality
 */
async function testAutoCashOut() {
  console.log('\n🤖 Testing auto cash out at 1.5x...');
  
  const response = await makeRequest('/api/game/start', {
    method: 'POST',
    body: JSON.stringify({
      game_type: 'crash',
      bet_amount: 10,
      auto_cash_out: 1.5
    })
  });

  const data = await response.json();
  
  if (data.success) {
    console.log('✅ Auto cash out game started!');
    console.log(`   Auto cash out at: ${data.game?.auto_cash_out || 1.5}x`);
    
    // Wait and see if auto cash out works
    console.log('⏳ Waiting for auto cash out...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    return data.game;
  } else {
    console.log('❌ Failed to start auto cash out game:', data.error);
    return null;
  }
}

/**
 * Main test runner
 */
async function runCompleteTest() {
  console.log('🚀 Starting Complete Crash Game Test\n');
  
  try {
    // Setup
    await setupTestAccount();
    
    // Test 1: Basic bet and manual cash out
    console.log('\n' + '='.repeat(50));
    console.log('TEST 1: Manual Cash Out');
    console.log('='.repeat(50));
    
    const gameState1 = await testPlaceBet(5);
    if (gameState1) {
      await testRocketFlight(gameState1, 2.0);
    }
    
    // Wait a bit between tests
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Test 2: Let rocket crash (don't cash out)
    console.log('\n' + '='.repeat(50));
    console.log('TEST 2: Let Rocket Crash');
    console.log('='.repeat(50));
    
    const gameState2 = await testPlaceBet(3);
    if (gameState2) {
      await testRocketFlight(gameState2, 10.0); // High target, likely to crash first
    }
    
    // Wait a bit between tests
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Test 3: Auto cash out
    console.log('\n' + '='.repeat(50));
    console.log('TEST 3: Auto Cash Out');
    console.log('='.repeat(50));
    
    await testAutoCashOut();
    
    console.log('\n🎉 Complete crash game test finished!');
    console.log('\nKey observations:');
    console.log('- Check if rocket moves up at 45-degree angle');
    console.log('- Verify multiplier increases smoothly');
    console.log('- Confirm crash happens at random points');
    console.log('- Test manual and auto cash out functionality');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
  }
}

// Run the complete test
runCompleteTest().catch(console.error);
