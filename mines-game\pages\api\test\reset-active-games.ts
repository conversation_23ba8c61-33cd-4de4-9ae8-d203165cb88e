import { NextApiRequest, NextApiResponse } from 'next';
import { gameDb, initDatabase } from '@/lib/database';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    await initDatabase();
    
    // Update all active games to lost status
    const db = require('@/lib/database').getDatabase();
    const stmt = db.prepare('UPDATE games SET status = ? WHERE status = ?');
    const result = stmt.run('lost', 'active');
    
    console.log(`🔄 Reset ${result.changes} active games to lost status`);
    
    return res.status(200).json({
      success: true,
      message: `Reset ${result.changes} active games`,
      gamesReset: result.changes
    });
  } catch (error) {
    console.error('Error resetting active games:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
}
