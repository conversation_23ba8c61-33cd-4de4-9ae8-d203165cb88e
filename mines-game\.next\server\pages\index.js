/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "(pages-dir-node)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-slot */ \"@radix-ui/react-slot\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! class-variance-authority */ \"class-variance-authority\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(pages-dir-node)/./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__, class_variance_authority__WEBPACK_IMPORTED_MODULE_3__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__]);\n([_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__, class_variance_authority__WEBPACK_IMPORTED_MODULE_3__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_3__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(pages-dir-node)/./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_utils__WEBPACK_IMPORTED_MODULE_2__]);\n_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/ui/toast.tsx":
/*!*********************************!*\
  !*** ./components/ui/toast.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle),\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-toast */ \"@radix-ui/react-toast\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! class-variance-authority */ \"class-variance-authority\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(pages-dir-node)/__barrel_optimize__?names=X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(pages-dir-node)/./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__, class_variance_authority__WEBPACK_IMPORTED_MODULE_3__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__]);\n([_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__, class_variance_authority__WEBPACK_IMPORTED_MODULE_3__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nconst ToastProvider = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Provider;\nconst ToastViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Viewport, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 14,\n        columnNumber: 3\n    }, undefined));\nToastViewport.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Viewport.displayName;\nconst toastVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_3__.cva)(\"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\", {\n    variants: {\n        variant: {\n            default: \"border bg-background text-foreground\",\n            destructive: \"destructive border-destructive bg-destructive text-destructive-foreground\",\n            success: \"border-green-500 bg-green-500/10 text-green-400\",\n            warning: \"border-yellow-500 bg-yellow-500/10 text-yellow-400\",\n            info: \"border-blue-500 bg-blue-500/10 text-blue-400\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(toastVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, undefined);\n});\nToast.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Root.displayName;\nconst ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Action, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nToastAction.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Action.displayName;\nconst ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Close, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\", className),\n        \"toast-close\": \"\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__.X, {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\ui\\\\toast.tsx\",\n            lineNumber: 87,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 78,\n        columnNumber: 3\n    }, undefined));\nToastClose.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Close.displayName;\nconst ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"text-sm font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 96,\n        columnNumber: 3\n    }, undefined));\nToastTitle.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Title.displayName;\nconst ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"text-sm opacity-90\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 108,\n        columnNumber: 3\n    }, undefined));\nToastDescription.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_2__.Description.displayName;\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/ui/toast.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(pages-dir-node)/./lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_utils__WEBPACK_IMPORTED_MODULE_2__]);\n_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Check if user is authenticated on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            checkAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const checkAuth = async ()=>{\n        try {\n            const response = await fetch(_lib_utils__WEBPACK_IMPORTED_MODULE_2__.API_ENDPOINTS.AUTH.ME, {\n                credentials: 'include'\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success && data.user) {\n                    setUser(data.user);\n                }\n            }\n        } catch (error) {\n            console.error('Auth check failed:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const login = async (email, password)=>{\n        try {\n            setLoading(true);\n            const response = await fetch(_lib_utils__WEBPACK_IMPORTED_MODULE_2__.API_ENDPOINTS.AUTH.LOGIN, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include',\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            const data = await response.json();\n            if (data.success && data.user) {\n                setUser(data.user);\n                return true;\n            } else {\n                console.error('Login failed:', data.error);\n                return false;\n            }\n        } catch (error) {\n            console.error('Login error:', error);\n            return false;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const signup = async (username, email, password)=>{\n        try {\n            setLoading(true);\n            const response = await fetch(_lib_utils__WEBPACK_IMPORTED_MODULE_2__.API_ENDPOINTS.AUTH.SIGNUP, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include',\n                body: JSON.stringify({\n                    username,\n                    email,\n                    password\n                })\n            });\n            const data = await response.json();\n            if (data.success && data.user) {\n                setUser(data.user);\n                return true;\n            } else {\n                console.error('Signup failed:', data.error);\n                return false;\n            }\n        } catch (error) {\n            console.error('Signup error:', error);\n            return false;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const logout = async ()=>{\n        try {\n            await fetch(_lib_utils__WEBPACK_IMPORTED_MODULE_2__.API_ENDPOINTS.AUTH.LOGOUT, {\n                method: 'POST',\n                credentials: 'include'\n            });\n        } catch (error) {\n            console.error('Logout error:', error);\n        } finally{\n            setUser(null);\n        }\n    };\n    const value = {\n        user,\n        login,\n        signup,\n        logout,\n        loading\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 118,\n        columnNumber: 10\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./contexts/CrashGameContext.tsx":
/*!***************************************!*\
  !*** ./contexts/CrashGameContext.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CrashGameProvider: () => (/* binding */ CrashGameProvider),\n/* harmony export */   useCrashGame: () => (/* binding */ useCrashGame)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _UniversalGameContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./UniversalGameContext */ \"(pages-dir-node)/./contexts/UniversalGameContext.tsx\");\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthContext */ \"(pages-dir-node)/./contexts/AuthContext.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_UniversalGameContext__WEBPACK_IMPORTED_MODULE_2__, _AuthContext__WEBPACK_IMPORTED_MODULE_3__]);\n([_UniversalGameContext__WEBPACK_IMPORTED_MODULE_2__, _AuthContext__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst CrashGameContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction CrashGameProvider({ children }) {\n    const universalGame = (0,_UniversalGameContext__WEBPACK_IMPORTED_MODULE_2__.useUniversalGame)();\n    const { user } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    // Real-time state for crash game\n    const [currentMultiplier, setCurrentMultiplier] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1.0);\n    const [timeElapsed, setTimeElapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [roundPhase, setRoundPhase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('waiting');\n    const [timeUntilNextRound, setTimeUntilNextRound] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Refs for intervals\n    const gameLoopRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const roundTimerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Ensure we're working with a crash game\n    const crashGame = universalGame.currentGameType === 'crash' ? universalGame.currentGame : null;\n    const crashHistory = universalGame.gameHistory.filter((game)=>game.game_type === 'crash');\n    /**\n   * Start a new crash game\n   */ const startGame = async (betAmount, autoCashOut)=>{\n        return universalGame.startGame('crash', {\n            bet_amount: betAmount,\n            auto_cash_out: autoCashOut\n        });\n    };\n    /**\n   * Cash out the current crash game\n   */ const cashOut = async ()=>{\n        try {\n            const result = await universalGame.cashOut();\n            return {\n                success: result.success,\n                profit: result.profit,\n                multiplier: currentMultiplier\n            };\n        } catch (error) {\n            console.error('Cash out error:', error);\n            return {\n                success: false,\n                profit: 0,\n                multiplier: currentMultiplier\n            };\n        }\n    };\n    /**\n   * Reset the game state\n   */ const resetGame = ()=>{\n        universalGame.resetGame();\n        setCurrentMultiplier(1.0);\n        setTimeElapsed(0);\n        setRoundPhase('waiting');\n        setTimeUntilNextRound(0);\n    };\n    /**\n   * Switch to crash game mode\n   */ const switchToCrash = ()=>{\n        universalGame.switchGame('crash');\n    };\n    /**\n   * Load crash game history\n   */ const loadGameHistory = async ()=>{\n        await universalGame.loadGameHistory('crash');\n    };\n    /**\n   * Check if player can cash out\n   */ const canCashOut = ()=>{\n        return crashGame?.status === 'active' && roundPhase === 'flying' && !crashGame?.cashed_out;\n    };\n    /**\n   * Check if player can place a bet\n   */ const canPlaceBet = ()=>{\n        return roundPhase === 'betting' && !crashGame;\n    };\n    /**\n   * Get current multiplier\n   */ const getCurrentMultiplier = ()=>{\n        return currentMultiplier;\n    };\n    /**\n   * Get time elapsed in current round\n   */ const getTimeElapsed = ()=>{\n        return timeElapsed;\n    };\n    /**\n   * Get current round phase\n   */ const getRoundPhase = ()=>{\n        return roundPhase;\n    };\n    /**\n   * Get time until next round\n   */ const getTimeUntilNextRound = ()=>{\n        return timeUntilNextRound;\n    };\n    /**\n   * Get crash game statistics\n   */ const getCrashStats = ()=>{\n        if (!crashGame) return null;\n        return {\n            betAmount: crashGame.bet_amount,\n            currentMultiplier: currentMultiplier,\n            potentialPayout: crashGame.bet_amount * currentMultiplier,\n            phase: roundPhase,\n            timeElapsed: timeElapsed,\n            autoCashOut: crashGame.auto_cash_out,\n            profit: crashGame.profit,\n            status: crashGame.status\n        };\n    };\n    /**\n   * Start the game loop for real-time updates\n   */ const startGameLoop = ()=>{\n        if (gameLoopRef.current) {\n            clearInterval(gameLoopRef.current);\n        }\n        gameLoopRef.current = setInterval(()=>{\n            if (roundPhase === 'flying' && crashGame) {\n                const newTimeElapsed = timeElapsed + 50; // Update every 50ms\n                setTimeElapsed(newTimeElapsed);\n                // Calculate new multiplier\n                const newMultiplier = Math.pow(1.002, newTimeElapsed);\n                setCurrentMultiplier(Math.round(newMultiplier * 100) / 100);\n                // Check if we've hit the crash point\n                if (crashGame.crash_point && newMultiplier >= crashGame.crash_point) {\n                    setRoundPhase('crashed');\n                    stopGameLoop();\n                    startRoundTimer();\n                }\n                // Check for auto cash out\n                if (crashGame.auto_cash_out && newMultiplier >= crashGame.auto_cash_out && !crashGame.cashed_out) {\n                    cashOut();\n                }\n            }\n        }, 50); // Update every 50ms for smooth animation\n    };\n    /**\n   * Stop the game loop\n   */ const stopGameLoop = ()=>{\n        if (gameLoopRef.current) {\n            clearInterval(gameLoopRef.current);\n            gameLoopRef.current = null;\n        }\n    };\n    /**\n   * Start round timer for betting/waiting phases\n   */ const startRoundTimer = ()=>{\n        if (roundTimerRef.current) {\n            clearTimeout(roundTimerRef.current);\n        }\n        // After crash, wait 3 seconds then start betting phase\n        if (roundPhase === 'crashed') {\n            setTimeUntilNextRound(3000);\n            roundTimerRef.current = setTimeout(()=>{\n                setRoundPhase('betting');\n                setTimeUntilNextRound(5000);\n                setCurrentMultiplier(1.0);\n                setTimeElapsed(0);\n                // After 5 seconds of betting, start flying phase\n                roundTimerRef.current = setTimeout(()=>{\n                    setRoundPhase('flying');\n                    setTimeUntilNextRound(0);\n                    startGameLoop();\n                }, 5000);\n            }, 3000);\n        }\n    };\n    /**\n   * Initialize crash game rounds\n   */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CrashGameProvider.useEffect\": ()=>{\n            // Start with betting phase\n            setRoundPhase('betting');\n            setTimeUntilNextRound(5000);\n            // Start the round cycle\n            const initialTimer = setTimeout({\n                \"CrashGameProvider.useEffect.initialTimer\": ()=>{\n                    setRoundPhase('flying');\n                    setTimeUntilNextRound(0);\n                    startGameLoop();\n                }\n            }[\"CrashGameProvider.useEffect.initialTimer\"], 5000);\n            return ({\n                \"CrashGameProvider.useEffect\": ()=>{\n                    clearTimeout(initialTimer);\n                    stopGameLoop();\n                    if (roundTimerRef.current) {\n                        clearTimeout(roundTimerRef.current);\n                    }\n                }\n            })[\"CrashGameProvider.useEffect\"];\n        }\n    }[\"CrashGameProvider.useEffect\"], []);\n    /**\n   * Handle game state changes\n   */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CrashGameProvider.useEffect\": ()=>{\n            if (crashGame) {\n                if (crashGame.phase) {\n                    setRoundPhase(crashGame.phase);\n                }\n                if (crashGame.time_elapsed !== undefined) {\n                    setTimeElapsed(crashGame.time_elapsed);\n                }\n                if (crashGame.current_multiplier) {\n                    setCurrentMultiplier(crashGame.current_multiplier);\n                }\n            }\n        }\n    }[\"CrashGameProvider.useEffect\"], [\n        crashGame\n    ]);\n    const value = {\n        gameState: crashGame,\n        gameHistory: crashHistory,\n        loading: universalGame.loading,\n        error: universalGame.error,\n        startGame,\n        cashOut,\n        resetGame,\n        makeMove: cashOut,\n        // Crash-specific methods\n        switchToCrash,\n        loadGameHistory,\n        canCashOut,\n        canPlaceBet,\n        getCurrentMultiplier,\n        getTimeElapsed,\n        getRoundPhase,\n        getTimeUntilNextRound,\n        getCrashStats\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CrashGameContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\contexts\\\\CrashGameContext.tsx\",\n        lineNumber: 288,\n        columnNumber: 5\n    }, this);\n}\nfunction useCrashGame() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CrashGameContext);\n    if (context === undefined) {\n        throw new Error('useCrashGame must be used within a CrashGameProvider');\n    }\n    return context;\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./contexts/CrashGameContext.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./contexts/DiceGameContext.tsx":
/*!**************************************!*\
  !*** ./contexts/DiceGameContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DiceGameProvider: () => (/* binding */ DiceGameProvider),\n/* harmony export */   useDiceGame: () => (/* binding */ useDiceGame)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _UniversalGameContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./UniversalGameContext */ \"(pages-dir-node)/./contexts/UniversalGameContext.tsx\");\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthContext */ \"(pages-dir-node)/./contexts/AuthContext.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_UniversalGameContext__WEBPACK_IMPORTED_MODULE_2__, _AuthContext__WEBPACK_IMPORTED_MODULE_3__]);\n([_UniversalGameContext__WEBPACK_IMPORTED_MODULE_2__, _AuthContext__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst DiceGameContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction DiceGameProvider({ children }) {\n    const universalGame = (0,_UniversalGameContext__WEBPACK_IMPORTED_MODULE_2__.useUniversalGame)();\n    const { user } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    // Ensure we're working with a dice game\n    const diceGame = universalGame.currentGameType === 'dice' ? universalGame.currentGame : null;\n    const diceHistory = universalGame.gameHistory.filter((game)=>game.game_type === 'dice');\n    /**\n   * Start a new dice game\n   */ const startGame = async (betAmount, targetNumber, rollUnder)=>{\n        return universalGame.startGame('dice', {\n            bet_amount: betAmount,\n            target_number: targetNumber,\n            roll_under: rollUnder\n        });\n    };\n    /**\n   * Roll the dice\n   */ const rollDice = async ()=>{\n        if (!diceGame || diceGame.status !== 'active') {\n            throw new Error('No active dice game');\n        }\n        try {\n            const result = await universalGame.makeMove('dice', {\n                game_id: diceGame.id\n            });\n            if (result.success && result.gameState) {\n                const updatedGame = result.gameState;\n                return {\n                    result: updatedGame.result || 0,\n                    won: updatedGame.status === 'won',\n                    multiplier: updatedGame.current_multiplier,\n                    profit: updatedGame.profit\n                };\n            } else {\n                throw new Error(result.error || 'Failed to roll dice');\n            }\n        } catch (error) {\n            console.error('Error rolling dice:', error);\n            throw error;\n        }\n    };\n    /**\n   * Reset game state\n   */ const resetGame = ()=>{\n        universalGame.resetGame();\n    };\n    /**\n   * Switch to dice game type\n   */ const switchToDice = ()=>{\n        universalGame.switchGame('dice');\n    };\n    /**\n   * Load game history\n   */ const loadGameHistory = async ()=>{\n        await universalGame.loadGameHistory();\n    };\n    /**\n   * Check if player can roll dice\n   */ const canRollDice = ()=>{\n        return diceGame?.status === 'active' && diceGame.result === undefined;\n    };\n    /**\n   * Get dice game statistics\n   */ const getDiceStats = ()=>{\n        if (!diceGame) return null;\n        const winChance = diceGame.roll_under ? diceGame.target_number - 1 : 100 - diceGame.target_number;\n        return {\n            targetNumber: diceGame.target_number,\n            rollUnder: diceGame.roll_under,\n            winChance: winChance,\n            multiplier: diceGame.current_multiplier,\n            result: diceGame.result,\n            profit: diceGame.profit,\n            status: diceGame.status\n        };\n    };\n    /**\n   * Calculate win chance for given parameters\n   */ const calculateWinChance = (targetNumber, rollUnder)=>{\n        if (rollUnder) {\n            return targetNumber - 1;\n        } else {\n            return 100 - targetNumber;\n        }\n    };\n    /**\n   * Calculate multiplier for given parameters\n   */ const calculateMultiplier = (targetNumber, rollUnder)=>{\n        const winChance = calculateWinChance(targetNumber, rollUnder);\n        const houseEdge = 0.01; // 1%\n        const multiplier = (100 - houseEdge * 100) / winChance;\n        return Math.max(1.01, Math.round(multiplier * 10000) / 10000);\n    };\n    const value = {\n        gameState: diceGame,\n        gameHistory: diceHistory,\n        loading: universalGame.loading,\n        error: universalGame.error,\n        startGame,\n        rollDice,\n        resetGame,\n        makeMove: rollDice,\n        // Dice-specific methods\n        switchToDice,\n        loadGameHistory,\n        canRollDice,\n        getDiceStats,\n        calculateWinChance,\n        calculateMultiplier\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DiceGameContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\contexts\\\\DiceGameContext.tsx\",\n        lineNumber: 156,\n        columnNumber: 5\n    }, this);\n}\nfunction useDiceGame() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(DiceGameContext);\n    if (context === undefined) {\n        throw new Error('useDiceGame must be used within a DiceGameProvider');\n    }\n    return context;\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./contexts/DiceGameContext.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./contexts/MinesGameContext.tsx":
/*!***************************************!*\
  !*** ./contexts/MinesGameContext.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MinesGameProvider: () => (/* binding */ MinesGameProvider),\n/* harmony export */   useGame: () => (/* binding */ useGame),\n/* harmony export */   useMinesGame: () => (/* binding */ useMinesGame)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _UniversalGameContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./UniversalGameContext */ \"(pages-dir-node)/./contexts/UniversalGameContext.tsx\");\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthContext */ \"(pages-dir-node)/./contexts/AuthContext.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_UniversalGameContext__WEBPACK_IMPORTED_MODULE_2__, _AuthContext__WEBPACK_IMPORTED_MODULE_3__]);\n([_UniversalGameContext__WEBPACK_IMPORTED_MODULE_2__, _AuthContext__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst MinesGameContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction MinesGameProvider({ children }) {\n    const universalGame = (0,_UniversalGameContext__WEBPACK_IMPORTED_MODULE_2__.useUniversalGame)();\n    const { user } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    // Ensure we're working with a mines game\n    const minesGame = universalGame.currentGameType === 'mines' ? universalGame.currentGame : null;\n    const minesHistory = universalGame.gameHistory.filter((game)=>game.game_type === 'mines');\n    /**\n   * Start a new mines game\n   */ const startGame = async (betAmount, mineCount)=>{\n        return universalGame.startGame('mines', {\n            bet_amount: betAmount,\n            mine_count: mineCount\n        });\n    };\n    /**\n   * Reveal a cell in the mines game\n   */ const revealCell = async (cellIndex)=>{\n        try {\n            console.log('🎯 MinesGameContext: Attempting to reveal cell', cellIndex);\n            console.log('🎯 MinesGameContext: Current minesGame state:', minesGame);\n            console.log('🎯 MinesGameContext: Universal game active:', universalGame.isGameActive());\n            const result = await universalGame.makeMove({\n                cellIndex\n            });\n            console.log('🎯 MinesGameContext: Move result:', result);\n            return {\n                hit: result.hit || false,\n                multiplier: result.multiplier || minesGame?.current_multiplier || 1,\n                gameOver: result.gameOver || false,\n                profit: result.profit\n            };\n        } catch (error) {\n            console.error('🎯 MinesGameContext: Reveal cell error:', error);\n            throw error;\n        }\n    };\n    /**\n   * Cash out the current mines game\n   */ const cashOut = async ()=>{\n        return universalGame.cashOut();\n    };\n    /**\n   * Reset the game state\n   */ const resetGame = ()=>{\n        universalGame.resetGame();\n    };\n    /**\n   * Switch to mines game mode\n   */ const switchToMines = ()=>{\n        universalGame.switchGame('mines');\n    };\n    /**\n   * Load mines game history\n   */ const loadGameHistory = async ()=>{\n        await universalGame.loadGameHistory('mines');\n    };\n    /**\n   * Check if player can cash out\n   */ const canCashOut = ()=>{\n        return minesGame?.status === 'active' && (minesGame?.revealed_cells?.length || 0) > 0;\n    };\n    /**\n   * Get safe cells remaining\n   */ const getSafeCellsRemaining = ()=>{\n        if (!minesGame) return 0;\n        const totalSafeCells = (minesGame.grid_size || 25) - minesGame.mine_count;\n        return totalSafeCells - minesGame.revealed_cells.length;\n    };\n    /**\n   * Get next multiplier if a safe cell is revealed\n   */ const getNextMultiplier = ()=>{\n        if (!minesGame || minesGame.status !== 'active') {\n            return minesGame?.current_multiplier || 1.0;\n        }\n        // This would need to be calculated by the game provider\n        // For now, return current multiplier\n        return minesGame.current_multiplier;\n    };\n    /**\n   * Check if a cell is revealed\n   */ const isCellRevealed = (cellIndex)=>{\n        return minesGame?.revealed_cells?.includes(cellIndex) || false;\n    };\n    /**\n   * Check if a cell is a mine (only visible when game is over)\n   */ const isCellMine = (cellIndex)=>{\n        if (!minesGame || minesGame.status === 'active') return false;\n        return minesGame.mine_positions?.includes(cellIndex) || false;\n    };\n    /**\n   * Get game statistics\n   */ const getGameStats = ()=>{\n        if (!minesGame) return null;\n        return {\n            gridSize: minesGame.grid_size || 25,\n            mineCount: minesGame.mine_count,\n            revealedCells: minesGame.revealed_cells.length,\n            safeCellsRemaining: getSafeCellsRemaining(),\n            currentMultiplier: minesGame.current_multiplier,\n            profit: minesGame.profit,\n            status: minesGame.status\n        };\n    };\n    const value = {\n        gameState: minesGame,\n        gameHistory: minesHistory,\n        loading: universalGame.loading,\n        error: universalGame.error,\n        startGame,\n        revealCell,\n        cashOut,\n        resetGame,\n        makeMove: revealCell,\n        // Mines-specific methods\n        switchToMines,\n        loadGameHistory,\n        canCashOut,\n        getSafeCellsRemaining,\n        getNextMultiplier,\n        isCellRevealed,\n        isCellMine,\n        getGameStats\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MinesGameContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\contexts\\\\MinesGameContext.tsx\",\n        lineNumber: 174,\n        columnNumber: 5\n    }, this);\n}\nfunction useMinesGame() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(MinesGameContext);\n    if (context === undefined) {\n        throw new Error('useMinesGame must be used within a MinesGameProvider');\n    }\n    return context;\n}\n// For backward compatibility, also export as useGame\nconst useGame = useMinesGame;\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./contexts/MinesGameContext.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./contexts/UniversalGameContext.tsx":
/*!*******************************************!*\
  !*** ./contexts/UniversalGameContext.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UniversalGameProvider: () => (/* binding */ UniversalGameProvider),\n/* harmony export */   useUniversalGame: () => (/* binding */ useUniversalGame)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthContext */ \"(pages-dir-node)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_games_GameFactory__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/games/GameFactory */ \"(pages-dir-node)/./lib/games/GameFactory.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_AuthContext__WEBPACK_IMPORTED_MODULE_2__]);\n_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst UniversalGameContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction UniversalGameProvider({ children }) {\n    const [currentGame, setCurrentGame] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentGameType, setCurrentGameType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [gameHistory, setGameHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { user } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    // Initialize game factory and load active game when user changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UniversalGameProvider.useEffect\": ()=>{\n            if (user) {\n                initializeAndLoadGame();\n            } else {\n                resetGame();\n            }\n        }\n    }[\"UniversalGameProvider.useEffect\"], [\n        user\n    ]);\n    /**\n   * Initialize game factory and load any active game\n   */ const initializeAndLoadGame = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            // Ensure game factory is initialized\n            await _lib_games_GameFactory__WEBPACK_IMPORTED_MODULE_3__.gameFactory.initialize();\n            // Load any active game\n            await loadActiveGame();\n        } catch (err) {\n            console.error('Failed to initialize games:', err);\n            setError('Failed to initialize games');\n        } finally{\n            setLoading(false);\n        }\n    };\n    /**\n   * Load active game from server\n   */ const loadActiveGame = async ()=>{\n        try {\n            const response = await fetch('/api/game/active', {\n                credentials: 'include'\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success && data.game) {\n                    setCurrentGame(data.game);\n                    setCurrentGameType(data.game.game_type);\n                }\n            }\n        } catch (err) {\n            console.error('Failed to load active game:', err);\n        }\n    };\n    /**\n   * Start a new game\n   */ const startGame = async (gameType, params)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            // Ensure we have the game type set\n            if (currentGameType !== gameType) {\n                setCurrentGameType(gameType);\n            }\n            const requestBody = {\n                game_type: gameType,\n                ...params\n            };\n            console.log('🌍 Frontend sending request:', JSON.stringify(requestBody, null, 2));\n            const response = await fetch('/api/game/start', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include',\n                body: JSON.stringify(requestBody)\n            });\n            const data = await response.json();\n            if (data.success && data.game) {\n                setCurrentGame(data.game);\n                return true;\n            } else {\n                console.error('Game start failed:', data.error);\n                setError(data.error || 'Failed to start game');\n                return false;\n            }\n        } catch (err) {\n            console.error('Start game error:', err);\n            setError('Failed to start game');\n            return false;\n        } finally{\n            setLoading(false);\n        }\n    };\n    /**\n   * Make a move in the current game\n   */ const makeMove = async (params)=>{\n        console.log('🎯 UniversalGameContext: makeMove called with params:', params);\n        console.log('🎯 UniversalGameContext: currentGame:', currentGame);\n        console.log('🎯 UniversalGameContext: currentGameType:', currentGameType);\n        // First try to reload active game if we don't have one\n        if (!currentGame || !currentGameType) {\n            console.log('🎯 UniversalGameContext: No current game, attempting to reload...');\n            await loadActiveGame();\n            // If still no game after reload, this might be a timing issue\n            if (!currentGame || !currentGameType) {\n                console.warn('🎯 UniversalGameContext: No active game found after reload');\n                // Return a failure result instead of throwing\n                return {\n                    success: false,\n                    hit: true,\n                    gameOver: true,\n                    error: 'No active game'\n                };\n            }\n        }\n        try {\n            setLoading(true);\n            setError(null);\n            console.log('🎯 UniversalGameContext: Making API call to /api/game/move');\n            const response = await fetch('/api/game/move', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include',\n                body: JSON.stringify({\n                    game_id: currentGame.id,\n                    game_type: currentGameType,\n                    ...params\n                })\n            });\n            const data = await response.json();\n            console.log('🎯 UniversalGameContext: API response:', data);\n            if (data.success) {\n                // Update current game state\n                if (data.gameState) {\n                    setCurrentGame(data.gameState);\n                }\n                // If game is over, refresh history and clear current game\n                if (data.gameOver) {\n                    setTimeout(()=>{\n                        loadGameHistory(currentGameType);\n                        if (data.gameState?.status !== 'active') {\n                            setCurrentGame(null);\n                        }\n                    }, 2000);\n                }\n                return data;\n            } else {\n                console.error('🎯 UniversalGameContext: Move failed:', data.error);\n                setError(data.error || 'Move failed');\n                // Return failure result instead of throwing\n                return {\n                    success: false,\n                    hit: true,\n                    gameOver: true,\n                    error: data.error || 'Move failed'\n                };\n            }\n        } catch (err) {\n            console.error('🎯 UniversalGameContext: Make move error:', err);\n            setError(err instanceof Error ? err.message : 'Move failed');\n            // Return failure result instead of throwing\n            return {\n                success: false,\n                hit: true,\n                gameOver: true,\n                error: err instanceof Error ? err.message : 'Move failed'\n            };\n        } finally{\n            setLoading(false);\n        }\n    };\n    /**\n   * Cash out current game\n   */ const cashOut = async ()=>{\n        if (!currentGame) {\n            throw new Error('No active game');\n        }\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await fetch('/api/game/cashout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include',\n                body: JSON.stringify({\n                    game_id: currentGame.id,\n                    game_type: currentGameType\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                // Update game state\n                if (data.gameState) {\n                    setCurrentGame(data.gameState);\n                }\n                // Refresh history and clear current game\n                setTimeout(()=>{\n                    if (currentGameType) {\n                        loadGameHistory(currentGameType);\n                    }\n                    setCurrentGame(null);\n                }, 2000);\n                return {\n                    success: true,\n                    profit: data.profit || 0\n                };\n            } else {\n                setError(data.error || 'Cash out failed');\n                return {\n                    success: false,\n                    profit: 0\n                };\n            }\n        } catch (err) {\n            console.error('Cash out error:', err);\n            setError('Cash out failed');\n            return {\n                success: false,\n                profit: 0\n            };\n        } finally{\n            setLoading(false);\n        }\n    };\n    /**\n   * Reset current game state\n   */ const resetGame = ()=>{\n        setCurrentGame(null);\n        setCurrentGameType(null);\n        setGameHistory([]);\n        setError(null);\n    };\n    /**\n   * Force reset any active games (cancels active game and refunds bet)\n   */ const forceResetActiveGame = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch('/api/game/reset-active', {\n                method: 'POST',\n                credentials: 'include'\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success) {\n                    resetGame();\n                    await loadActiveGame(); // Reload to confirm reset\n                    return true;\n                }\n            }\n            return false;\n        } catch (err) {\n            console.error('Failed to reset active game:', err);\n            return false;\n        } finally{\n            setLoading(false);\n        }\n    };\n    /**\n   * Switch to a different game type\n   */ const switchGame = (gameType)=>{\n        if (currentGame?.status === 'active') {\n            console.warn('Cannot switch games while a game is active');\n            return;\n        }\n        setCurrentGameType(gameType);\n        setCurrentGame(null);\n        setError(null);\n        // Load history for the new game type\n        loadGameHistory(gameType);\n    };\n    /**\n   * Load game history\n   */ const loadGameHistory = async (gameType)=>{\n        try {\n            const typeParam = gameType || currentGameType;\n            const url = typeParam ? `/api/game/history?game_type=${typeParam}` : '/api/game/history';\n            const response = await fetch(url, {\n                credentials: 'include'\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success && data.games) {\n                    setGameHistory(data.games);\n                }\n            }\n        } catch (err) {\n            console.error('Failed to load game history:', err);\n        }\n    };\n    /**\n   * Get game configuration\n   */ const getGameConfig = (gameType)=>{\n        return _lib_games_GameFactory__WEBPACK_IMPORTED_MODULE_3__.gameFactory.getGameConfig(gameType);\n    };\n    /**\n   * Check if current game is active\n   */ const isGameActive = ()=>{\n        return currentGame?.status === 'active';\n    };\n    /**\n   * Check if player can cash out\n   */ const canCashOut = ()=>{\n        if (!currentGame || !currentGameType) return false;\n        const provider = _lib_games_GameFactory__WEBPACK_IMPORTED_MODULE_3__.gameFactory.getGameProvider(currentGameType);\n        if (!provider) return false;\n        // For mines game, check if any cells are revealed\n        if (currentGameType === 'mines') {\n            return isGameActive() && currentGame.revealed_cells?.length > 0;\n        }\n        // Default: can cash out if game is active and has positive multiplier\n        return isGameActive() && currentGame.current_multiplier > 1;\n    };\n    const value = {\n        currentGame,\n        currentGameType,\n        gameHistory,\n        loading,\n        error,\n        startGame,\n        makeMove,\n        cashOut,\n        resetGame,\n        forceResetActiveGame,\n        switchGame,\n        loadGameHistory,\n        getGameConfig,\n        isGameActive,\n        canCashOut\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UniversalGameContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\contexts\\\\UniversalGameContext.tsx\",\n        lineNumber: 436,\n        columnNumber: 5\n    }, this);\n}\nfunction useUniversalGame() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(UniversalGameContext);\n    if (context === undefined) {\n        throw new Error('useUniversalGame must be used within a UniversalGameProvider');\n    }\n    return context;\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./contexts/UniversalGameContext.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./lib/crypto.ts":
/*!***********************!*\
  !*** ./lib/crypto.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateOutcomeHash: () => (/* binding */ calculateOutcomeHash),\n/* harmony export */   createGameHash: () => (/* binding */ createGameHash),\n/* harmony export */   createHMAC: () => (/* binding */ createHMAC),\n/* harmony export */   decrypt: () => (/* binding */ decrypt),\n/* harmony export */   encrypt: () => (/* binding */ encrypt),\n/* harmony export */   generateClientSeed: () => (/* binding */ generateClientSeed),\n/* harmony export */   generateGameId: () => (/* binding */ generateGameId),\n/* harmony export */   generateJWTSecret: () => (/* binding */ generateJWTSecret),\n/* harmony export */   generateMinePositions: () => (/* binding */ generateMinePositions),\n/* harmony export */   generateNonce: () => (/* binding */ generateNonce),\n/* harmony export */   generateServerSeed: () => (/* binding */ generateServerSeed),\n/* harmony export */   generateSessionToken: () => (/* binding */ generateSessionToken),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   hashServerSeed: () => (/* binding */ hashServerSeed),\n/* harmony export */   verifyGameIntegrity: () => (/* binding */ verifyGameIntegrity),\n/* harmony export */   verifyHMAC: () => (/* binding */ verifyHMAC),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_0__);\n\n/**\n * Generate a cryptographically secure random server seed\n */ function generateServerSeed() {\n    return crypto__WEBPACK_IMPORTED_MODULE_0___default().randomBytes(32).toString('hex');\n}\n/**\n * Generate a random client seed\n */ function generateClientSeed() {\n    return crypto__WEBPACK_IMPORTED_MODULE_0___default().randomBytes(16).toString('hex');\n}\n/**\n * Create SHA-256 hash of combined seeds\n */ function createGameHash(serverSeed, clientSeed, nonce = 0) {\n    const combined = `${serverSeed}:${clientSeed}:${nonce}`;\n    return crypto__WEBPACK_IMPORTED_MODULE_0___default().createHash('sha256').update(combined).digest('hex');\n}\n/**\n * Generate mine positions using provably fair algorithm\n * This ensures the mine placement is deterministic based on seeds but unpredictable\n */ function generateMinePositions(serverSeed, clientSeed, mineCount, gridSize = 25) {\n    const hash = createGameHash(serverSeed, clientSeed);\n    const mines = [];\n    const availablePositions = Array.from({\n        length: gridSize\n    }, (_, i)=>i);\n    // Use hash bytes to determine mine positions\n    let hashIndex = 0;\n    while(mines.length < mineCount && availablePositions.length > 0){\n        // Get next 4 bytes from hash and convert to number\n        const bytes = hash.slice(hashIndex * 8, (hashIndex + 1) * 8);\n        const randomValue = parseInt(bytes, 16);\n        // Use modulo to get position within available positions\n        const positionIndex = randomValue % availablePositions.length;\n        const position = availablePositions[positionIndex];\n        mines.push(position);\n        availablePositions.splice(positionIndex, 1);\n        hashIndex++;\n        // If we run out of hash bytes, create a new hash with incremented nonce\n        if (hashIndex * 8 >= hash.length) {\n            const newHash = createGameHash(serverSeed, clientSeed, hashIndex);\n            const extendedHash = hash + newHash;\n            hashIndex = hash.length / 8;\n        }\n    }\n    return mines.sort((a, b)=>a - b);\n}\n/**\n * Verify game integrity by recalculating mine positions\n */ function verifyGameIntegrity(serverSeed, clientSeed, mineCount, expectedMines, gridSize = 25) {\n    const calculatedMines = generateMinePositions(serverSeed, clientSeed, mineCount, gridSize);\n    if (calculatedMines.length !== expectedMines.length) {\n        return false;\n    }\n    return calculatedMines.every((mine, index)=>mine === expectedMines[index]);\n}\n/**\n * Create a hash of the server seed for client verification\n * This allows clients to verify the server seed wasn't changed after the game\n */ function hashServerSeed(serverSeed) {\n    return crypto__WEBPACK_IMPORTED_MODULE_0___default().createHash('sha256').update(serverSeed).digest('hex');\n}\n/**\n * Generate a secure random nonce\n */ function generateNonce() {\n    return crypto__WEBPACK_IMPORTED_MODULE_0___default().randomInt(0, 2147483647); // Max 32-bit signed integer\n}\n/**\n * Create HMAC signature for API security\n */ function createHMAC(data, secret) {\n    return crypto__WEBPACK_IMPORTED_MODULE_0___default().createHmac('sha256', secret).update(data).digest('hex');\n}\n/**\n * Verify HMAC signature\n */ function verifyHMAC(data, signature, secret) {\n    const expectedSignature = createHMAC(data, secret);\n    return crypto__WEBPACK_IMPORTED_MODULE_0___default().timingSafeEqual(Buffer.from(signature), Buffer.from(expectedSignature));\n}\n/**\n * Generate a secure session token\n */ function generateSessionToken() {\n    return crypto__WEBPACK_IMPORTED_MODULE_0___default().randomBytes(32).toString('hex');\n}\n/**\n * Hash password using bcrypt-compatible method\n */ function hashPassword(password) {\n    const bcrypt = __webpack_require__(/*! bcryptjs */ \"bcryptjs?a1e7\");\n    return bcrypt.hashSync(password, 12);\n}\n/**\n * Verify password against hash\n */ function verifyPassword(password, hash) {\n    const bcrypt = __webpack_require__(/*! bcryptjs */ \"bcryptjs?a1e7\");\n    return bcrypt.compareSync(password, hash);\n}\n/**\n * Generate JWT secret key\n */ function generateJWTSecret() {\n    return crypto__WEBPACK_IMPORTED_MODULE_0___default().randomBytes(64).toString('hex');\n}\n/**\n * Calculate game outcome hash for transparency\n */ function calculateOutcomeHash(serverSeed, clientSeed, minePositions, revealedCells) {\n    const outcomeData = {\n        serverSeed,\n        clientSeed,\n        minePositions,\n        revealedCells,\n        timestamp: Date.now()\n    };\n    return crypto__WEBPACK_IMPORTED_MODULE_0___default().createHash('sha256').update(JSON.stringify(outcomeData)).digest('hex');\n}\n/**\n * Generate a unique game ID\n */ function generateGameId() {\n    const timestamp = Date.now().toString(36);\n    const random = crypto__WEBPACK_IMPORTED_MODULE_0___default().randomBytes(8).toString('hex');\n    return `${timestamp}-${random}`;\n}\n/**\n * Encrypt sensitive data\n */ function encrypt(text, key) {\n    const algorithm = 'aes-256-gcm';\n    const iv = crypto__WEBPACK_IMPORTED_MODULE_0___default().randomBytes(16);\n    const cipher = crypto__WEBPACK_IMPORTED_MODULE_0___default().createCipher(algorithm, key);\n    let encrypted = cipher.update(text, 'utf8', 'hex');\n    encrypted += cipher.final('hex');\n    return iv.toString('hex') + ':' + encrypted;\n}\n/**\n * Decrypt sensitive data\n */ function decrypt(encryptedText, key) {\n    const algorithm = 'aes-256-gcm';\n    const parts = encryptedText.split(':');\n    const iv = Buffer.from(parts[0], 'hex');\n    const encrypted = parts[1];\n    const decipher = crypto__WEBPACK_IMPORTED_MODULE_0___default().createDecipher(algorithm, key);\n    let decrypted = decipher.update(encrypted, 'hex', 'utf8');\n    decrypted += decipher.final('utf8');\n    return decrypted;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL2xpYi9jcnlwdG8udHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUE0QjtBQUU1Qjs7Q0FFQyxHQUNNLFNBQVNDO0lBQ2QsT0FBT0QseURBQWtCLENBQUMsSUFBSUcsUUFBUSxDQUFDO0FBQ3pDO0FBRUE7O0NBRUMsR0FDTSxTQUFTQztJQUNkLE9BQU9KLHlEQUFrQixDQUFDLElBQUlHLFFBQVEsQ0FBQztBQUN6QztBQUVBOztDQUVDLEdBQ00sU0FBU0UsZUFBZUMsVUFBa0IsRUFBRUMsVUFBa0IsRUFBRUMsUUFBZ0IsQ0FBQztJQUN0RixNQUFNQyxXQUFXLEdBQUdILFdBQVcsQ0FBQyxFQUFFQyxXQUFXLENBQUMsRUFBRUMsT0FBTztJQUN2RCxPQUFPUix3REFBaUIsQ0FBQyxVQUFVVyxNQUFNLENBQUNGLFVBQVVHLE1BQU0sQ0FBQztBQUM3RDtBQUVBOzs7Q0FHQyxHQUNNLFNBQVNDLHNCQUNkUCxVQUFrQixFQUNsQkMsVUFBa0IsRUFDbEJPLFNBQWlCLEVBQ2pCQyxXQUFtQixFQUFFO0lBRXJCLE1BQU1DLE9BQU9YLGVBQWVDLFlBQVlDO0lBQ3hDLE1BQU1VLFFBQWtCLEVBQUU7SUFDMUIsTUFBTUMscUJBQXFCQyxNQUFNQyxJQUFJLENBQUM7UUFBRUMsUUFBUU47SUFBUyxHQUFHLENBQUNPLEdBQUdDLElBQU1BO0lBRXRFLDZDQUE2QztJQUM3QyxJQUFJQyxZQUFZO0lBRWhCLE1BQU9QLE1BQU1JLE1BQU0sR0FBR1AsYUFBYUksbUJBQW1CRyxNQUFNLEdBQUcsRUFBRztRQUNoRSxtREFBbUQ7UUFDbkQsTUFBTUksUUFBUVQsS0FBS1UsS0FBSyxDQUFDRixZQUFZLEdBQUcsQ0FBQ0EsWUFBWSxLQUFLO1FBQzFELE1BQU1HLGNBQWNDLFNBQVNILE9BQU87UUFFcEMsd0RBQXdEO1FBQ3hELE1BQU1JLGdCQUFnQkYsY0FBY1QsbUJBQW1CRyxNQUFNO1FBQzdELE1BQU1TLFdBQVdaLGtCQUFrQixDQUFDVyxjQUFjO1FBRWxEWixNQUFNYyxJQUFJLENBQUNEO1FBQ1haLG1CQUFtQmMsTUFBTSxDQUFDSCxlQUFlO1FBRXpDTDtRQUVBLHdFQUF3RTtRQUN4RSxJQUFJQSxZQUFZLEtBQUtSLEtBQUtLLE1BQU0sRUFBRTtZQUNoQyxNQUFNWSxVQUFVNUIsZUFBZUMsWUFBWUMsWUFBWWlCO1lBQ3ZELE1BQU1VLGVBQWVsQixPQUFPaUI7WUFDNUJULFlBQVlSLEtBQUtLLE1BQU0sR0FBRztRQUM1QjtJQUNGO0lBRUEsT0FBT0osTUFBTWtCLElBQUksQ0FBQyxDQUFDQyxHQUFHQyxJQUFNRCxJQUFJQztBQUNsQztBQUVBOztDQUVDLEdBQ00sU0FBU0Msb0JBQ2RoQyxVQUFrQixFQUNsQkMsVUFBa0IsRUFDbEJPLFNBQWlCLEVBQ2pCeUIsYUFBdUIsRUFDdkJ4QixXQUFtQixFQUFFO0lBRXJCLE1BQU15QixrQkFBa0IzQixzQkFBc0JQLFlBQVlDLFlBQVlPLFdBQVdDO0lBRWpGLElBQUl5QixnQkFBZ0JuQixNQUFNLEtBQUtrQixjQUFjbEIsTUFBTSxFQUFFO1FBQ25ELE9BQU87SUFDVDtJQUVBLE9BQU9tQixnQkFBZ0JDLEtBQUssQ0FBQyxDQUFDQyxNQUFNQyxRQUFVRCxTQUFTSCxhQUFhLENBQUNJLE1BQU07QUFDN0U7QUFFQTs7O0NBR0MsR0FDTSxTQUFTQyxlQUFldEMsVUFBa0I7SUFDL0MsT0FBT04sd0RBQWlCLENBQUMsVUFBVVcsTUFBTSxDQUFDTCxZQUFZTSxNQUFNLENBQUM7QUFDL0Q7QUFFQTs7Q0FFQyxHQUNNLFNBQVNpQztJQUNkLE9BQU83Qyx1REFBZ0IsQ0FBQyxHQUFHLGFBQWEsNEJBQTRCO0FBQ3RFO0FBRUE7O0NBRUMsR0FDTSxTQUFTK0MsV0FBV0MsSUFBWSxFQUFFQyxNQUFjO0lBQ3JELE9BQU9qRCx3REFBaUIsQ0FBQyxVQUFVaUQsUUFBUXRDLE1BQU0sQ0FBQ3FDLE1BQU1wQyxNQUFNLENBQUM7QUFDakU7QUFFQTs7Q0FFQyxHQUNNLFNBQVN1QyxXQUFXSCxJQUFZLEVBQUVJLFNBQWlCLEVBQUVILE1BQWM7SUFDeEUsTUFBTUksb0JBQW9CTixXQUFXQyxNQUFNQztJQUMzQyxPQUFPakQsNkRBQXNCLENBQUN1RCxPQUFPbkMsSUFBSSxDQUFDZ0MsWUFBWUcsT0FBT25DLElBQUksQ0FBQ2lDO0FBQ3BFO0FBRUE7O0NBRUMsR0FDTSxTQUFTRztJQUNkLE9BQU94RCx5REFBa0IsQ0FBQyxJQUFJRyxRQUFRLENBQUM7QUFDekM7QUFFQTs7Q0FFQyxHQUNNLFNBQVNzRCxhQUFhQyxRQUFnQjtJQUMzQyxNQUFNQyxTQUFTQyxtQkFBT0EsQ0FBQywrQkFBVTtJQUNqQyxPQUFPRCxPQUFPRSxRQUFRLENBQUNILFVBQVU7QUFDbkM7QUFFQTs7Q0FFQyxHQUNNLFNBQVNJLGVBQWVKLFFBQWdCLEVBQUUxQyxJQUFZO0lBQzNELE1BQU0yQyxTQUFTQyxtQkFBT0EsQ0FBQywrQkFBVTtJQUNqQyxPQUFPRCxPQUFPSSxXQUFXLENBQUNMLFVBQVUxQztBQUN0QztBQUVBOztDQUVDLEdBQ00sU0FBU2dEO0lBQ2QsT0FBT2hFLHlEQUFrQixDQUFDLElBQUlHLFFBQVEsQ0FBQztBQUN6QztBQUVBOztDQUVDLEdBQ00sU0FBUzhELHFCQUNkM0QsVUFBa0IsRUFDbEJDLFVBQWtCLEVBQ2xCMkQsYUFBdUIsRUFDdkJDLGFBQXVCO0lBRXZCLE1BQU1DLGNBQWM7UUFDbEI5RDtRQUNBQztRQUNBMkQ7UUFDQUM7UUFDQUUsV0FBV0MsS0FBS0MsR0FBRztJQUNyQjtJQUVBLE9BQU92RSx3REFBaUIsQ0FBQyxVQUFVVyxNQUFNLENBQUM2RCxLQUFLQyxTQUFTLENBQUNMLGNBQWN4RCxNQUFNLENBQUM7QUFDaEY7QUFFQTs7Q0FFQyxHQUNNLFNBQVM4RDtJQUNkLE1BQU1MLFlBQVlDLEtBQUtDLEdBQUcsR0FBR3BFLFFBQVEsQ0FBQztJQUN0QyxNQUFNd0UsU0FBUzNFLHlEQUFrQixDQUFDLEdBQUdHLFFBQVEsQ0FBQztJQUM5QyxPQUFPLEdBQUdrRSxVQUFVLENBQUMsRUFBRU0sUUFBUTtBQUNqQztBQUVBOztDQUVDLEdBQ00sU0FBU0MsUUFBUUMsSUFBWSxFQUFFQyxHQUFXO0lBQy9DLE1BQU1DLFlBQVk7SUFDbEIsTUFBTUMsS0FBS2hGLHlEQUFrQixDQUFDO0lBQzlCLE1BQU1pRixTQUFTakYsMERBQW1CLENBQUMrRSxXQUFXRDtJQUU5QyxJQUFJSyxZQUFZRixPQUFPdEUsTUFBTSxDQUFDa0UsTUFBTSxRQUFRO0lBQzVDTSxhQUFhRixPQUFPRyxLQUFLLENBQUM7SUFFMUIsT0FBT0osR0FBRzdFLFFBQVEsQ0FBQyxTQUFTLE1BQU1nRjtBQUNwQztBQUVBOztDQUVDLEdBQ00sU0FBU0UsUUFBUUMsYUFBcUIsRUFBRVIsR0FBVztJQUN4RCxNQUFNQyxZQUFZO0lBQ2xCLE1BQU1RLFFBQVFELGNBQWNFLEtBQUssQ0FBQztJQUNsQyxNQUFNUixLQUFLekIsT0FBT25DLElBQUksQ0FBQ21FLEtBQUssQ0FBQyxFQUFFLEVBQUU7SUFDakMsTUFBTUosWUFBWUksS0FBSyxDQUFDLEVBQUU7SUFFMUIsTUFBTUUsV0FBV3pGLDREQUFxQixDQUFDK0UsV0FBV0Q7SUFDbEQsSUFBSWEsWUFBWUYsU0FBUzlFLE1BQU0sQ0FBQ3dFLFdBQVcsT0FBTztJQUNsRFEsYUFBYUYsU0FBU0wsS0FBSyxDQUFDO0lBRTVCLE9BQU9PO0FBQ1QiLCJzb3VyY2VzIjpbIkU6XFwxMTFcXFBST0pFQ1RcXG1pbmVzLWdhbWVcXGxpYlxcY3J5cHRvLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcnlwdG8gZnJvbSAnY3J5cHRvJztcblxuLyoqXG4gKiBHZW5lcmF0ZSBhIGNyeXB0b2dyYXBoaWNhbGx5IHNlY3VyZSByYW5kb20gc2VydmVyIHNlZWRcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdlbmVyYXRlU2VydmVyU2VlZCgpOiBzdHJpbmcge1xuICByZXR1cm4gY3J5cHRvLnJhbmRvbUJ5dGVzKDMyKS50b1N0cmluZygnaGV4Jyk7XG59XG5cbi8qKlxuICogR2VuZXJhdGUgYSByYW5kb20gY2xpZW50IHNlZWRcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdlbmVyYXRlQ2xpZW50U2VlZCgpOiBzdHJpbmcge1xuICByZXR1cm4gY3J5cHRvLnJhbmRvbUJ5dGVzKDE2KS50b1N0cmluZygnaGV4Jyk7XG59XG5cbi8qKlxuICogQ3JlYXRlIFNIQS0yNTYgaGFzaCBvZiBjb21iaW5lZCBzZWVkc1xuICovXG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlR2FtZUhhc2goc2VydmVyU2VlZDogc3RyaW5nLCBjbGllbnRTZWVkOiBzdHJpbmcsIG5vbmNlOiBudW1iZXIgPSAwKTogc3RyaW5nIHtcbiAgY29uc3QgY29tYmluZWQgPSBgJHtzZXJ2ZXJTZWVkfToke2NsaWVudFNlZWR9OiR7bm9uY2V9YDtcbiAgcmV0dXJuIGNyeXB0by5jcmVhdGVIYXNoKCdzaGEyNTYnKS51cGRhdGUoY29tYmluZWQpLmRpZ2VzdCgnaGV4Jyk7XG59XG5cbi8qKlxuICogR2VuZXJhdGUgbWluZSBwb3NpdGlvbnMgdXNpbmcgcHJvdmFibHkgZmFpciBhbGdvcml0aG1cbiAqIFRoaXMgZW5zdXJlcyB0aGUgbWluZSBwbGFjZW1lbnQgaXMgZGV0ZXJtaW5pc3RpYyBiYXNlZCBvbiBzZWVkcyBidXQgdW5wcmVkaWN0YWJsZVxuICovXG5leHBvcnQgZnVuY3Rpb24gZ2VuZXJhdGVNaW5lUG9zaXRpb25zKFxuICBzZXJ2ZXJTZWVkOiBzdHJpbmcsXG4gIGNsaWVudFNlZWQ6IHN0cmluZyxcbiAgbWluZUNvdW50OiBudW1iZXIsXG4gIGdyaWRTaXplOiBudW1iZXIgPSAyNVxuKTogbnVtYmVyW10ge1xuICBjb25zdCBoYXNoID0gY3JlYXRlR2FtZUhhc2goc2VydmVyU2VlZCwgY2xpZW50U2VlZCk7XG4gIGNvbnN0IG1pbmVzOiBudW1iZXJbXSA9IFtdO1xuICBjb25zdCBhdmFpbGFibGVQb3NpdGlvbnMgPSBBcnJheS5mcm9tKHsgbGVuZ3RoOiBncmlkU2l6ZSB9LCAoXywgaSkgPT4gaSk7XG4gIFxuICAvLyBVc2UgaGFzaCBieXRlcyB0byBkZXRlcm1pbmUgbWluZSBwb3NpdGlvbnNcbiAgbGV0IGhhc2hJbmRleCA9IDA7XG4gIFxuICB3aGlsZSAobWluZXMubGVuZ3RoIDwgbWluZUNvdW50ICYmIGF2YWlsYWJsZVBvc2l0aW9ucy5sZW5ndGggPiAwKSB7XG4gICAgLy8gR2V0IG5leHQgNCBieXRlcyBmcm9tIGhhc2ggYW5kIGNvbnZlcnQgdG8gbnVtYmVyXG4gICAgY29uc3QgYnl0ZXMgPSBoYXNoLnNsaWNlKGhhc2hJbmRleCAqIDgsIChoYXNoSW5kZXggKyAxKSAqIDgpO1xuICAgIGNvbnN0IHJhbmRvbVZhbHVlID0gcGFyc2VJbnQoYnl0ZXMsIDE2KTtcbiAgICBcbiAgICAvLyBVc2UgbW9kdWxvIHRvIGdldCBwb3NpdGlvbiB3aXRoaW4gYXZhaWxhYmxlIHBvc2l0aW9uc1xuICAgIGNvbnN0IHBvc2l0aW9uSW5kZXggPSByYW5kb21WYWx1ZSAlIGF2YWlsYWJsZVBvc2l0aW9ucy5sZW5ndGg7XG4gICAgY29uc3QgcG9zaXRpb24gPSBhdmFpbGFibGVQb3NpdGlvbnNbcG9zaXRpb25JbmRleF07XG4gICAgXG4gICAgbWluZXMucHVzaChwb3NpdGlvbik7XG4gICAgYXZhaWxhYmxlUG9zaXRpb25zLnNwbGljZShwb3NpdGlvbkluZGV4LCAxKTtcbiAgICBcbiAgICBoYXNoSW5kZXgrKztcbiAgICBcbiAgICAvLyBJZiB3ZSBydW4gb3V0IG9mIGhhc2ggYnl0ZXMsIGNyZWF0ZSBhIG5ldyBoYXNoIHdpdGggaW5jcmVtZW50ZWQgbm9uY2VcbiAgICBpZiAoaGFzaEluZGV4ICogOCA+PSBoYXNoLmxlbmd0aCkge1xuICAgICAgY29uc3QgbmV3SGFzaCA9IGNyZWF0ZUdhbWVIYXNoKHNlcnZlclNlZWQsIGNsaWVudFNlZWQsIGhhc2hJbmRleCk7XG4gICAgICBjb25zdCBleHRlbmRlZEhhc2ggPSBoYXNoICsgbmV3SGFzaDtcbiAgICAgIGhhc2hJbmRleCA9IGhhc2gubGVuZ3RoIC8gODtcbiAgICB9XG4gIH1cbiAgXG4gIHJldHVybiBtaW5lcy5zb3J0KChhLCBiKSA9PiBhIC0gYik7XG59XG5cbi8qKlxuICogVmVyaWZ5IGdhbWUgaW50ZWdyaXR5IGJ5IHJlY2FsY3VsYXRpbmcgbWluZSBwb3NpdGlvbnNcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHZlcmlmeUdhbWVJbnRlZ3JpdHkoXG4gIHNlcnZlclNlZWQ6IHN0cmluZyxcbiAgY2xpZW50U2VlZDogc3RyaW5nLFxuICBtaW5lQ291bnQ6IG51bWJlcixcbiAgZXhwZWN0ZWRNaW5lczogbnVtYmVyW10sXG4gIGdyaWRTaXplOiBudW1iZXIgPSAyNVxuKTogYm9vbGVhbiB7XG4gIGNvbnN0IGNhbGN1bGF0ZWRNaW5lcyA9IGdlbmVyYXRlTWluZVBvc2l0aW9ucyhzZXJ2ZXJTZWVkLCBjbGllbnRTZWVkLCBtaW5lQ291bnQsIGdyaWRTaXplKTtcbiAgXG4gIGlmIChjYWxjdWxhdGVkTWluZXMubGVuZ3RoICE9PSBleHBlY3RlZE1pbmVzLmxlbmd0aCkge1xuICAgIHJldHVybiBmYWxzZTtcbiAgfVxuICBcbiAgcmV0dXJuIGNhbGN1bGF0ZWRNaW5lcy5ldmVyeSgobWluZSwgaW5kZXgpID0+IG1pbmUgPT09IGV4cGVjdGVkTWluZXNbaW5kZXhdKTtcbn1cblxuLyoqXG4gKiBDcmVhdGUgYSBoYXNoIG9mIHRoZSBzZXJ2ZXIgc2VlZCBmb3IgY2xpZW50IHZlcmlmaWNhdGlvblxuICogVGhpcyBhbGxvd3MgY2xpZW50cyB0byB2ZXJpZnkgdGhlIHNlcnZlciBzZWVkIHdhc24ndCBjaGFuZ2VkIGFmdGVyIHRoZSBnYW1lXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBoYXNoU2VydmVyU2VlZChzZXJ2ZXJTZWVkOiBzdHJpbmcpOiBzdHJpbmcge1xuICByZXR1cm4gY3J5cHRvLmNyZWF0ZUhhc2goJ3NoYTI1NicpLnVwZGF0ZShzZXJ2ZXJTZWVkKS5kaWdlc3QoJ2hleCcpO1xufVxuXG4vKipcbiAqIEdlbmVyYXRlIGEgc2VjdXJlIHJhbmRvbSBub25jZVxuICovXG5leHBvcnQgZnVuY3Rpb24gZ2VuZXJhdGVOb25jZSgpOiBudW1iZXIge1xuICByZXR1cm4gY3J5cHRvLnJhbmRvbUludCgwLCAyMTQ3NDgzNjQ3KTsgLy8gTWF4IDMyLWJpdCBzaWduZWQgaW50ZWdlclxufVxuXG4vKipcbiAqIENyZWF0ZSBITUFDIHNpZ25hdHVyZSBmb3IgQVBJIHNlY3VyaXR5XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVITUFDKGRhdGE6IHN0cmluZywgc2VjcmV0OiBzdHJpbmcpOiBzdHJpbmcge1xuICByZXR1cm4gY3J5cHRvLmNyZWF0ZUhtYWMoJ3NoYTI1NicsIHNlY3JldCkudXBkYXRlKGRhdGEpLmRpZ2VzdCgnaGV4Jyk7XG59XG5cbi8qKlxuICogVmVyaWZ5IEhNQUMgc2lnbmF0dXJlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB2ZXJpZnlITUFDKGRhdGE6IHN0cmluZywgc2lnbmF0dXJlOiBzdHJpbmcsIHNlY3JldDogc3RyaW5nKTogYm9vbGVhbiB7XG4gIGNvbnN0IGV4cGVjdGVkU2lnbmF0dXJlID0gY3JlYXRlSE1BQyhkYXRhLCBzZWNyZXQpO1xuICByZXR1cm4gY3J5cHRvLnRpbWluZ1NhZmVFcXVhbChCdWZmZXIuZnJvbShzaWduYXR1cmUpLCBCdWZmZXIuZnJvbShleHBlY3RlZFNpZ25hdHVyZSkpO1xufVxuXG4vKipcbiAqIEdlbmVyYXRlIGEgc2VjdXJlIHNlc3Npb24gdG9rZW5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdlbmVyYXRlU2Vzc2lvblRva2VuKCk6IHN0cmluZyB7XG4gIHJldHVybiBjcnlwdG8ucmFuZG9tQnl0ZXMoMzIpLnRvU3RyaW5nKCdoZXgnKTtcbn1cblxuLyoqXG4gKiBIYXNoIHBhc3N3b3JkIHVzaW5nIGJjcnlwdC1jb21wYXRpYmxlIG1ldGhvZFxuICovXG5leHBvcnQgZnVuY3Rpb24gaGFzaFBhc3N3b3JkKHBhc3N3b3JkOiBzdHJpbmcpOiBzdHJpbmcge1xuICBjb25zdCBiY3J5cHQgPSByZXF1aXJlKCdiY3J5cHRqcycpO1xuICByZXR1cm4gYmNyeXB0Lmhhc2hTeW5jKHBhc3N3b3JkLCAxMik7XG59XG5cbi8qKlxuICogVmVyaWZ5IHBhc3N3b3JkIGFnYWluc3QgaGFzaFxuICovXG5leHBvcnQgZnVuY3Rpb24gdmVyaWZ5UGFzc3dvcmQocGFzc3dvcmQ6IHN0cmluZywgaGFzaDogc3RyaW5nKTogYm9vbGVhbiB7XG4gIGNvbnN0IGJjcnlwdCA9IHJlcXVpcmUoJ2JjcnlwdGpzJyk7XG4gIHJldHVybiBiY3J5cHQuY29tcGFyZVN5bmMocGFzc3dvcmQsIGhhc2gpO1xufVxuXG4vKipcbiAqIEdlbmVyYXRlIEpXVCBzZWNyZXQga2V5XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZW5lcmF0ZUpXVFNlY3JldCgpOiBzdHJpbmcge1xuICByZXR1cm4gY3J5cHRvLnJhbmRvbUJ5dGVzKDY0KS50b1N0cmluZygnaGV4Jyk7XG59XG5cbi8qKlxuICogQ2FsY3VsYXRlIGdhbWUgb3V0Y29tZSBoYXNoIGZvciB0cmFuc3BhcmVuY3lcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNhbGN1bGF0ZU91dGNvbWVIYXNoKFxuICBzZXJ2ZXJTZWVkOiBzdHJpbmcsXG4gIGNsaWVudFNlZWQ6IHN0cmluZyxcbiAgbWluZVBvc2l0aW9uczogbnVtYmVyW10sXG4gIHJldmVhbGVkQ2VsbHM6IG51bWJlcltdXG4pOiBzdHJpbmcge1xuICBjb25zdCBvdXRjb21lRGF0YSA9IHtcbiAgICBzZXJ2ZXJTZWVkLFxuICAgIGNsaWVudFNlZWQsXG4gICAgbWluZVBvc2l0aW9ucyxcbiAgICByZXZlYWxlZENlbGxzLFxuICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKVxuICB9O1xuICBcbiAgcmV0dXJuIGNyeXB0by5jcmVhdGVIYXNoKCdzaGEyNTYnKS51cGRhdGUoSlNPTi5zdHJpbmdpZnkob3V0Y29tZURhdGEpKS5kaWdlc3QoJ2hleCcpO1xufVxuXG4vKipcbiAqIEdlbmVyYXRlIGEgdW5pcXVlIGdhbWUgSURcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdlbmVyYXRlR2FtZUlkKCk6IHN0cmluZyB7XG4gIGNvbnN0IHRpbWVzdGFtcCA9IERhdGUubm93KCkudG9TdHJpbmcoMzYpO1xuICBjb25zdCByYW5kb20gPSBjcnlwdG8ucmFuZG9tQnl0ZXMoOCkudG9TdHJpbmcoJ2hleCcpO1xuICByZXR1cm4gYCR7dGltZXN0YW1wfS0ke3JhbmRvbX1gO1xufVxuXG4vKipcbiAqIEVuY3J5cHQgc2Vuc2l0aXZlIGRhdGFcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGVuY3J5cHQodGV4dDogc3RyaW5nLCBrZXk6IHN0cmluZyk6IHN0cmluZyB7XG4gIGNvbnN0IGFsZ29yaXRobSA9ICdhZXMtMjU2LWdjbSc7XG4gIGNvbnN0IGl2ID0gY3J5cHRvLnJhbmRvbUJ5dGVzKDE2KTtcbiAgY29uc3QgY2lwaGVyID0gY3J5cHRvLmNyZWF0ZUNpcGhlcihhbGdvcml0aG0sIGtleSk7XG4gIFxuICBsZXQgZW5jcnlwdGVkID0gY2lwaGVyLnVwZGF0ZSh0ZXh0LCAndXRmOCcsICdoZXgnKTtcbiAgZW5jcnlwdGVkICs9IGNpcGhlci5maW5hbCgnaGV4Jyk7XG4gIFxuICByZXR1cm4gaXYudG9TdHJpbmcoJ2hleCcpICsgJzonICsgZW5jcnlwdGVkO1xufVxuXG4vKipcbiAqIERlY3J5cHQgc2Vuc2l0aXZlIGRhdGFcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGRlY3J5cHQoZW5jcnlwdGVkVGV4dDogc3RyaW5nLCBrZXk6IHN0cmluZyk6IHN0cmluZyB7XG4gIGNvbnN0IGFsZ29yaXRobSA9ICdhZXMtMjU2LWdjbSc7XG4gIGNvbnN0IHBhcnRzID0gZW5jcnlwdGVkVGV4dC5zcGxpdCgnOicpO1xuICBjb25zdCBpdiA9IEJ1ZmZlci5mcm9tKHBhcnRzWzBdLCAnaGV4Jyk7XG4gIGNvbnN0IGVuY3J5cHRlZCA9IHBhcnRzWzFdO1xuICBcbiAgY29uc3QgZGVjaXBoZXIgPSBjcnlwdG8uY3JlYXRlRGVjaXBoZXIoYWxnb3JpdGhtLCBrZXkpO1xuICBsZXQgZGVjcnlwdGVkID0gZGVjaXBoZXIudXBkYXRlKGVuY3J5cHRlZCwgJ2hleCcsICd1dGY4Jyk7XG4gIGRlY3J5cHRlZCArPSBkZWNpcGhlci5maW5hbCgndXRmOCcpO1xuICBcbiAgcmV0dXJuIGRlY3J5cHRlZDtcbn1cbiJdLCJuYW1lcyI6WyJjcnlwdG8iLCJnZW5lcmF0ZVNlcnZlclNlZWQiLCJyYW5kb21CeXRlcyIsInRvU3RyaW5nIiwiZ2VuZXJhdGVDbGllbnRTZWVkIiwiY3JlYXRlR2FtZUhhc2giLCJzZXJ2ZXJTZWVkIiwiY2xpZW50U2VlZCIsIm5vbmNlIiwiY29tYmluZWQiLCJjcmVhdGVIYXNoIiwidXBkYXRlIiwiZGlnZXN0IiwiZ2VuZXJhdGVNaW5lUG9zaXRpb25zIiwibWluZUNvdW50IiwiZ3JpZFNpemUiLCJoYXNoIiwibWluZXMiLCJhdmFpbGFibGVQb3NpdGlvbnMiLCJBcnJheSIsImZyb20iLCJsZW5ndGgiLCJfIiwiaSIsImhhc2hJbmRleCIsImJ5dGVzIiwic2xpY2UiLCJyYW5kb21WYWx1ZSIsInBhcnNlSW50IiwicG9zaXRpb25JbmRleCIsInBvc2l0aW9uIiwicHVzaCIsInNwbGljZSIsIm5ld0hhc2giLCJleHRlbmRlZEhhc2giLCJzb3J0IiwiYSIsImIiLCJ2ZXJpZnlHYW1lSW50ZWdyaXR5IiwiZXhwZWN0ZWRNaW5lcyIsImNhbGN1bGF0ZWRNaW5lcyIsImV2ZXJ5IiwibWluZSIsImluZGV4IiwiaGFzaFNlcnZlclNlZWQiLCJnZW5lcmF0ZU5vbmNlIiwicmFuZG9tSW50IiwiY3JlYXRlSE1BQyIsImRhdGEiLCJzZWNyZXQiLCJjcmVhdGVIbWFjIiwidmVyaWZ5SE1BQyIsInNpZ25hdHVyZSIsImV4cGVjdGVkU2lnbmF0dXJlIiwidGltaW5nU2FmZUVxdWFsIiwiQnVmZmVyIiwiZ2VuZXJhdGVTZXNzaW9uVG9rZW4iLCJoYXNoUGFzc3dvcmQiLCJwYXNzd29yZCIsImJjcnlwdCIsInJlcXVpcmUiLCJoYXNoU3luYyIsInZlcmlmeVBhc3N3b3JkIiwiY29tcGFyZVN5bmMiLCJnZW5lcmF0ZUpXVFNlY3JldCIsImNhbGN1bGF0ZU91dGNvbWVIYXNoIiwibWluZVBvc2l0aW9ucyIsInJldmVhbGVkQ2VsbHMiLCJvdXRjb21lRGF0YSIsInRpbWVzdGFtcCIsIkRhdGUiLCJub3ciLCJKU09OIiwic3RyaW5naWZ5IiwiZ2VuZXJhdGVHYW1lSWQiLCJyYW5kb20iLCJlbmNyeXB0IiwidGV4dCIsImtleSIsImFsZ29yaXRobSIsIml2IiwiY2lwaGVyIiwiY3JlYXRlQ2lwaGVyIiwiZW5jcnlwdGVkIiwiZmluYWwiLCJkZWNyeXB0IiwiZW5jcnlwdGVkVGV4dCIsInBhcnRzIiwic3BsaXQiLCJkZWNpcGhlciIsImNyZWF0ZURlY2lwaGVyIiwiZGVjcnlwdGVkIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./lib/crypto.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./lib/games/BaseGameProvider.ts":
/*!***************************************!*\
  !*** ./lib/games/BaseGameProvider.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseGameProvider: () => (/* binding */ BaseGameProvider),\n/* harmony export */   GameActionError: () => (/* binding */ GameActionError),\n/* harmony export */   GameActionType: () => (/* binding */ GameActionType),\n/* harmony export */   GameError: () => (/* binding */ GameError),\n/* harmony export */   GameNotActiveError: () => (/* binding */ GameNotActiveError),\n/* harmony export */   InsufficientFundsError: () => (/* binding */ InsufficientFundsError),\n/* harmony export */   InvalidGameParamsError: () => (/* binding */ InvalidGameParamsError)\n/* harmony export */ });\n/* harmony import */ var _lib_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/crypto */ \"(pages-dir-node)/./lib/crypto.ts\");\n\n/**\n * Abstract base class for all game providers\n * Implements common functionality and enforces interface compliance\n */ class BaseGameProvider {\n    /**\n   * Validate common game parameters\n   */ validateBaseParams(betAmount) {\n        if (typeof betAmount !== 'number' || betAmount <= 0) {\n            return false;\n        }\n        if (betAmount < this.config.minBet || betAmount > this.config.maxBet) {\n            return false;\n        }\n        return true;\n    }\n    /**\n   * Generate base game data common to all games\n   */ generateBaseGameData(userId, betAmount, clientSeed) {\n        return {\n            user_id: userId,\n            game_type: this.gameType,\n            bet_amount: betAmount,\n            current_multiplier: 1.0,\n            status: 'active',\n            server_seed: (0,_lib_crypto__WEBPACK_IMPORTED_MODULE_0__.generateServerSeed)(),\n            client_seed: clientSeed || (0,_lib_crypto__WEBPACK_IMPORTED_MODULE_0__.generateClientSeed)(),\n            profit: 0\n        };\n    }\n    /**\n   * Calculate profit based on bet amount and multiplier\n   */ calculateProfit(betAmount, multiplier) {\n        return betAmount * multiplier - betAmount;\n    }\n    /**\n   * Apply house edge to multiplier\n   */ applyHouseEdge(multiplier) {\n        return multiplier * (1 - this.config.houseEdge);\n    }\n    /**\n   * Validate that multiplier doesn't exceed maximum\n   */ validateMultiplier(multiplier) {\n        return multiplier <= this.config.maxMultiplier;\n    }\n    /**\n   * Generate provably fair hash for verification\n   */ generateGameHash(serverSeed, clientSeed, nonce = 0) {\n        const crypto = __webpack_require__(/*! crypto */ \"crypto\");\n        const combined = `${serverSeed}:${clientSeed}:${nonce}`;\n        return crypto.createHash('sha256').update(combined).digest('hex');\n    }\n    /**\n   * Generate random number from seeds (0-1)\n   */ generateRandomFromSeeds(serverSeed, clientSeed, nonce = 0) {\n        const hash = this.generateGameHash(serverSeed, clientSeed, nonce);\n        // Use first 8 characters of hash to generate number between 0-1\n        const hexValue = hash.substring(0, 8);\n        const intValue = parseInt(hexValue, 16);\n        return intValue / 0xffffffff;\n    }\n    /**\n   * Generate random integer within range using provably fair method\n   */ generateRandomInt(min, max, serverSeed, clientSeed, nonce = 0) {\n        const random = this.generateRandomFromSeeds(serverSeed, clientSeed, nonce);\n        return Math.floor(random * (max - min + 1)) + min;\n    }\n    /**\n   * Generate array of random integers (useful for mines, card games, etc.)\n   */ generateRandomArray(count, min, max, serverSeed, clientSeed, startNonce = 0) {\n        const result = [];\n        const range = max - min + 1;\n        for(let i = 0; i < count; i++){\n            const random = this.generateRandomFromSeeds(serverSeed, clientSeed, startNonce + i);\n            const value = Math.floor(random * range) + min;\n            result.push(value);\n        }\n        return result;\n    }\n    /**\n   * Shuffle array using Fisher-Yates algorithm with provably fair randomness\n   */ shuffleArray(array, serverSeed, clientSeed, startNonce = 0) {\n        const shuffled = [\n            ...array\n        ];\n        for(let i = shuffled.length - 1; i > 0; i--){\n            const random = this.generateRandomFromSeeds(serverSeed, clientSeed, startNonce + i);\n            const j = Math.floor(random * (i + 1));\n            [shuffled[i], shuffled[j]] = [\n                shuffled[j],\n                shuffled[i]\n            ];\n        }\n        return shuffled;\n    }\n    /**\n   * Validate game state belongs to user\n   */ validateGameOwnership(gameState, userId) {\n        return gameState.user_id === userId;\n    }\n    /**\n   * Check if game is in active state\n   */ isGameActive(gameState) {\n        return gameState.status === 'active';\n    }\n    /**\n   * Log game action for debugging/auditing\n   */ logGameAction(gameState, action, result) {\n        if (true) {\n            console.log(`[${this.gameType.toUpperCase()}] Game ${gameState.id}: ${action.type}`, {\n                payload: action.payload,\n                result,\n                timestamp: new Date().toISOString()\n            });\n        }\n    }\n}\n/**\n * Game action types enum for consistency\n */ var GameActionType = /*#__PURE__*/ function(GameActionType) {\n    GameActionType[\"START_GAME\"] = \"START_GAME\";\n    GameActionType[\"MAKE_MOVE\"] = \"MAKE_MOVE\";\n    GameActionType[\"CASH_OUT\"] = \"CASH_OUT\";\n    GameActionType[\"END_GAME\"] = \"END_GAME\";\n    GameActionType[\"CANCEL_GAME\"] = \"CANCEL_GAME\";\n    return GameActionType;\n}({});\n/**\n * Common game errors\n */ class GameError extends Error {\n    constructor(message, code, gameType){\n        super(message), this.code = code, this.gameType = gameType;\n        this.name = 'GameError';\n    }\n}\nclass InvalidGameParamsError extends GameError {\n    constructor(gameType, message = 'Invalid game parameters'){\n        super(message, 'INVALID_PARAMS', gameType);\n    }\n}\nclass GameNotActiveError extends GameError {\n    constructor(gameType, message = 'Game is not active'){\n        super(message, 'GAME_NOT_ACTIVE', gameType);\n    }\n}\nclass InsufficientFundsError extends GameError {\n    constructor(gameType, message = 'Insufficient funds'){\n        super(message, 'INSUFFICIENT_FUNDS', gameType);\n    }\n}\nclass GameActionError extends GameError {\n    constructor(message, gameType){\n        super(message, 'INVALID_ACTION', gameType);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./lib/games/BaseGameProvider.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./lib/games/GameFactory.ts":
/*!**********************************!*\
  !*** ./lib/games/GameFactory.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GameFactory: () => (/* binding */ GameFactory),\n/* harmony export */   gameFactory: () => (/* binding */ gameFactory)\n/* harmony export */ });\n/* harmony import */ var _registry__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./registry */ \"(pages-dir-node)/./lib/games/registry.ts\");\n/* harmony import */ var _mines_MinesGameProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mines/MinesGameProvider */ \"(pages-dir-node)/./lib/games/mines/MinesGameProvider.ts\");\n/* harmony import */ var _dice_DiceGameProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./dice/DiceGameProvider */ \"(pages-dir-node)/./lib/games/dice/DiceGameProvider.ts\");\n/* harmony import */ var _crash_CrashGameProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./crash/CrashGameProvider */ \"(pages-dir-node)/./lib/games/crash/CrashGameProvider.ts\");\n/* harmony import */ var _PlaceholderGameProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./PlaceholderGameProvider */ \"(pages-dir-node)/./lib/games/PlaceholderGameProvider.ts\");\n\n\n\n\n\n/**\n * Game Factory - Creates and manages game instances\n */ class GameFactory {\n    constructor(){\n        this.initialized = false;\n    }\n    /**\n   * Get singleton instance\n   */ static getInstance() {\n        if (!GameFactory.instance) {\n            GameFactory.instance = new GameFactory();\n        }\n        return GameFactory.instance;\n    }\n    /**\n   * Initialize all game providers\n   */ async initialize() {\n        if (this.initialized) {\n            console.log('🎮 Game factory already initialized');\n            return;\n        }\n        console.log('🎮 Initializing game factory...');\n        try {\n            // Register all game providers\n            await this.registerAllGames();\n            this.initialized = true;\n            console.log('✅ Game factory initialized successfully');\n            // Log registry stats\n            const stats = _registry__WEBPACK_IMPORTED_MODULE_0__.gameRegistry.getRegistryStats();\n            console.log(`📊 Registered ${stats.totalGames} games (${stats.activeGames} active)`);\n            console.log('📋 Games by category:', stats.gamesByCategory);\n        } catch (error) {\n            console.error('❌ Failed to initialize game factory:', error);\n            throw error;\n        }\n    }\n    /**\n   * Register all available game providers\n   */ async registerAllGames() {\n        const providers = [\n            new _mines_MinesGameProvider__WEBPACK_IMPORTED_MODULE_1__.MinesGameProvider(),\n            new _dice_DiceGameProvider__WEBPACK_IMPORTED_MODULE_2__.DiceGameProvider(),\n            new _crash_CrashGameProvider__WEBPACK_IMPORTED_MODULE_3__.CrashGameProvider(),\n            ...(0,_PlaceholderGameProvider__WEBPACK_IMPORTED_MODULE_4__.createPlaceholderProviders)()\n        ];\n        for (const provider of providers){\n            try {\n                _registry__WEBPACK_IMPORTED_MODULE_0__.gameRegistry.registerGame(provider.config, provider);\n            } catch (error) {\n                console.error(`Failed to register ${provider.gameType} game:`, error);\n            // Continue with other games even if one fails\n            }\n        }\n    }\n    /**\n   * Create a new game instance\n   */ async createGame(gameType, params) {\n        try {\n            if (!this.initialized) {\n                await this.initialize();\n            }\n            const provider = _registry__WEBPACK_IMPORTED_MODULE_0__.gameRegistry.getGameProvider(gameType);\n            if (!provider) {\n                return {\n                    success: false,\n                    error: `Game type '${gameType}' is not registered`\n                };\n            }\n            // Validate parameters\n            if (!provider.validateGameParams(params)) {\n                return {\n                    success: false,\n                    error: 'Invalid game parameters'\n                };\n            }\n            // Generate game data\n            const gameData = provider.generateGameData(params);\n            return {\n                success: true,\n                game: gameData\n            };\n        } catch (error) {\n            console.error(`Error creating ${gameType} game:`, error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n    /**\n   * Process a game action\n   */ async processGameAction(gameType, gameState, actionType, payload) {\n        try {\n            if (!this.initialized) {\n                await this.initialize();\n            }\n            const provider = _registry__WEBPACK_IMPORTED_MODULE_0__.gameRegistry.getGameProvider(gameType);\n            if (!provider) {\n                return {\n                    success: false,\n                    error: `Game type '${gameType}' is not registered`\n                };\n            }\n            const action = {\n                type: actionType,\n                payload\n            };\n            const updatedGameState = await provider.processGameAction(gameState, action);\n            return {\n                success: true,\n                gameState: updatedGameState\n            };\n        } catch (error) {\n            console.error(`Error processing ${gameType} action:`, error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n    /**\n   * Calculate multiplier for a game state\n   */ calculateMultiplier(gameType, gameState, params) {\n        if (!this.initialized) {\n            console.warn('Game factory not initialized, returning default multiplier');\n            return 1.0;\n        }\n        const provider = _registry__WEBPACK_IMPORTED_MODULE_0__.gameRegistry.getGameProvider(gameType);\n        if (!provider) {\n            console.warn(`Game type '${gameType}' not found, returning default multiplier`);\n            return 1.0;\n        }\n        return provider.calculateMultiplier(gameState, params);\n    }\n    /**\n   * Get game configuration\n   */ getGameConfig(gameType) {\n        return _registry__WEBPACK_IMPORTED_MODULE_0__.gameRegistry.getGameConfig(gameType);\n    }\n    /**\n   * Get all available games\n   */ getAllGames() {\n        return _registry__WEBPACK_IMPORTED_MODULE_0__.gameRegistry.getAllGames();\n    }\n    /**\n   * Get games by category\n   */ getGamesByCategory(category) {\n        return _registry__WEBPACK_IMPORTED_MODULE_0__.gameRegistry.getGamesByCategory(category);\n    }\n    /**\n   * Search games\n   */ searchGames(query) {\n        return _registry__WEBPACK_IMPORTED_MODULE_0__.gameRegistry.searchGames(query);\n    }\n    /**\n   * Check if game type is available\n   */ isGameAvailable(gameType) {\n        const config = _registry__WEBPACK_IMPORTED_MODULE_0__.gameRegistry.getGameConfig(gameType);\n        return config?.isActive ?? false;\n    }\n    /**\n   * Get game provider (for advanced usage)\n   */ getGameProvider(gameType) {\n        return _registry__WEBPACK_IMPORTED_MODULE_0__.gameRegistry.getGameProvider(gameType);\n    }\n    /**\n   * Reset factory (for testing)\n   */ reset() {\n        _registry__WEBPACK_IMPORTED_MODULE_0__.gameRegistry.clear();\n        this.initialized = false;\n        console.log('🔄 Game factory reset');\n    }\n    /**\n   * Get initialization status\n   */ isInitialized() {\n        return this.initialized;\n    }\n}\n// Export singleton instance\nconst gameFactory = GameFactory.getInstance();\n// Auto-initialize in production\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./lib/games/GameFactory.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./lib/games/PlaceholderGameProvider.ts":
/*!**********************************************!*\
  !*** ./lib/games/PlaceholderGameProvider.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PlaceholderGameProvider: () => (/* binding */ PlaceholderGameProvider),\n/* harmony export */   createPlaceholderProviders: () => (/* binding */ createPlaceholderProviders),\n/* harmony export */   placeholderGames: () => (/* binding */ placeholderGames)\n/* harmony export */ });\n/* harmony import */ var _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BaseGameProvider */ \"(pages-dir-node)/./lib/games/BaseGameProvider.ts\");\n\n/**\n * Placeholder Game Provider - For games that are not yet implemented\n * This allows us to show games in the lobby while they're under development\n */ class PlaceholderGameProvider extends _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__.BaseGameProvider {\n    constructor(config){\n        super();\n        this.gameType = config.id;\n        this.config = config;\n    }\n    /**\n   * Validate game parameters (placeholder implementation)\n   */ validateGameParams(params) {\n        // Basic validation for placeholder games\n        return params && typeof params.betAmount === 'number' && params.betAmount > 0;\n    }\n    /**\n   * Calculate multiplier (placeholder implementation)\n   */ calculateMultiplier(gameState, params) {\n        return gameState.current_multiplier || 1.0;\n    }\n    /**\n   * Generate game data (placeholder implementation)\n   */ generateGameData(params) {\n        const baseData = this.generateBaseGameData(params.userId, params.betAmount, params.clientSeed);\n        return {\n            ...baseData,\n            game_type: this.gameType\n        };\n    }\n    /**\n   * Process game action (placeholder implementation)\n   */ async processGameAction(gameState, action) {\n        // Placeholder games don't actually process actions\n        // They just return the current state\n        return gameState;\n    }\n}\n// Create placeholder game configurations\nconst placeholderGames = [\n    {\n        id: 'crash',\n        name: 'Crash',\n        description: 'Watch the multiplier climb and cash out before it crashes! The longer you wait, the higher the risk and reward.',\n        icon: '🚀',\n        category: 'originals',\n        minBet: 0.01,\n        maxBet: 1000,\n        houseEdge: 0.01,\n        maxMultiplier: 10000,\n        features: [\n            'Provably Fair',\n            'Live Multiplayer',\n            'Auto Cashout'\n        ],\n        isActive: true,\n        isFeatured: true,\n        isNew: false\n    },\n    {\n        id: 'plinko',\n        name: 'Plinko',\n        description: 'Drop balls down the plinko board and watch them bounce into different multiplier slots. Pure luck and excitement!',\n        icon: '🏀',\n        category: 'originals',\n        minBet: 0.01,\n        maxBet: 1000,\n        houseEdge: 0.01,\n        maxMultiplier: 1000,\n        features: [\n            'Provably Fair',\n            'Multiple Risk Levels',\n            'Animated Gameplay'\n        ],\n        isActive: true,\n        isFeatured: true,\n        isNew: false\n    },\n    {\n        id: 'limbo',\n        name: 'Limbo',\n        description: 'Choose your target multiplier and see if you can reach it. The higher the target, the lower the chance!',\n        icon: '🎯',\n        category: 'originals',\n        minBet: 0.01,\n        maxBet: 1000,\n        houseEdge: 0.01,\n        maxMultiplier: 1000000,\n        features: [\n            'Provably Fair',\n            'Instant Results',\n            'Unlimited Multiplier'\n        ],\n        isActive: true,\n        isFeatured: false,\n        isNew: false\n    },\n    {\n        id: 'wheel',\n        name: 'Wheel',\n        description: 'Spin the wheel of fortune and win big! Choose your risk level and watch the wheel decide your fate.',\n        icon: '🎡',\n        category: 'originals',\n        minBet: 0.01,\n        maxBet: 1000,\n        houseEdge: 0.01,\n        maxMultiplier: 50,\n        features: [\n            'Provably Fair',\n            'Multiple Risk Levels',\n            'Visual Spinning'\n        ],\n        isActive: true,\n        isFeatured: false,\n        isNew: true\n    },\n    {\n        id: 'blackjack',\n        name: 'Blackjack',\n        description: 'Classic card game where you try to get as close to 21 as possible without going over. Beat the dealer!',\n        icon: '🃏',\n        category: 'table',\n        minBet: 0.01,\n        maxBet: 1000,\n        houseEdge: 0.005,\n        maxMultiplier: 3,\n        features: [\n            'Classic Rules',\n            'Strategy Based',\n            'Low House Edge'\n        ],\n        isActive: true,\n        isFeatured: false,\n        isNew: false\n    },\n    {\n        id: 'roulette',\n        name: 'Roulette',\n        description: 'Place your bets on the roulette table and watch the ball spin. Red or black? Odd or even? Your choice!',\n        icon: '🎰',\n        category: 'table',\n        minBet: 0.01,\n        maxBet: 1000,\n        houseEdge: 0.027,\n        maxMultiplier: 36,\n        features: [\n            'European Rules',\n            'Multiple Bet Types',\n            'Live Animation'\n        ],\n        isActive: true,\n        isFeatured: false,\n        isNew: false\n    }\n];\n// Create placeholder providers for each game\nconst createPlaceholderProviders = ()=>{\n    return placeholderGames.map((config)=>new PlaceholderGameProvider(config));\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./lib/games/PlaceholderGameProvider.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./lib/games/crash/CrashGameProvider.ts":
/*!**********************************************!*\
  !*** ./lib/games/crash/CrashGameProvider.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CrashGameProvider: () => (/* binding */ CrashGameProvider)\n/* harmony export */ });\n/* harmony import */ var _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../BaseGameProvider */ \"(pages-dir-node)/./lib/games/BaseGameProvider.ts\");\n\n/**\n * Crash Game Provider - Handles crash game logic\n */ class CrashGameProvider extends _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__.BaseGameProvider {\n    /**\n   * Validate crash game parameters\n   */ validateGameParams(params) {\n        const { bet_amount, auto_cash_out } = params;\n        if (!this.validateBaseParams(bet_amount)) {\n            return false;\n        }\n        // Validate auto cash out if provided\n        if (auto_cash_out !== undefined) {\n            if (typeof auto_cash_out !== 'number' || auto_cash_out < 1.01) {\n                return false;\n            }\n            if (auto_cash_out > this.config.maxMultiplier) {\n                return false;\n            }\n        }\n        return true;\n    }\n    /**\n   * Calculate current multiplier based on time elapsed\n   */ calculateMultiplier(gameState, params) {\n        if (gameState.phase !== 'flying') {\n            return 1.0;\n        }\n        const timeElapsed = gameState.time_elapsed || 0;\n        // Use exponential growth: multiplier = 1.002^(time_in_ms)\n        // This creates a smooth curve that accelerates over time\n        const multiplier = Math.pow(1.002, timeElapsed);\n        // Cap at crash point if it exists\n        if (gameState.crash_point && multiplier >= gameState.crash_point) {\n            return gameState.crash_point;\n        }\n        return Math.round(multiplier * 100) / 100; // Round to 2 decimal places\n    }\n    /**\n   * Generate crash game data\n   */ generateGameData(params) {\n        const { bet_amount, auto_cash_out, user_id, client_seed } = params;\n        const baseData = this.generateBaseGameData(user_id, bet_amount, client_seed);\n        // Generate crash point using provably fair method\n        const crashPoint = this.generateCrashPoint(baseData.server_seed, baseData.client_seed);\n        return {\n            ...baseData,\n            game_type: 'crash',\n            crash_point: crashPoint,\n            auto_cash_out: auto_cash_out,\n            phase: 'betting',\n            time_elapsed: 0,\n            cashed_out: false,\n            round_id: this.generateRoundId()\n        };\n    }\n    /**\n   * Process crash game actions\n   */ async processGameAction(gameState, action) {\n        this.logGameAction(gameState, action);\n        switch(action.type){\n            case _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__.GameActionType.START_GAME:\n                return this.handleStartGame(gameState, action.payload);\n            case _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__.GameActionType.CASH_OUT:\n                return this.handleCashOut(gameState, action.payload);\n            case 'UPDATE_MULTIPLIER':\n                return this.handleUpdateMultiplier(gameState, action.payload);\n            case 'CRASH':\n                return this.handleCrash(gameState);\n            case 'START_ROUND':\n                return this.handleStartRound(gameState);\n            case 'END_ROUND':\n                return this.handleEndRound(gameState);\n            default:\n                throw new Error(`Unknown action type: ${action.type}`);\n        }\n    }\n    /**\n   * Handle starting the game (betting phase)\n   */ handleStartGame(gameState, payload) {\n        return {\n            ...gameState,\n            phase: 'betting',\n            status: 'active',\n            time_elapsed: 0,\n            current_multiplier: 1.0,\n            cashed_out: false\n        };\n    }\n    /**\n   * Handle cash out action\n   */ handleCashOut(gameState, payload) {\n        if (gameState.phase !== 'flying' || gameState.cashed_out) {\n            throw new Error('Cannot cash out at this time');\n        }\n        const currentMultiplier = this.calculateMultiplier(gameState);\n        const profit = this.calculateProfit(gameState.bet_amount, currentMultiplier);\n        return {\n            ...gameState,\n            cashed_out: true,\n            cash_out_at: currentMultiplier,\n            current_multiplier: currentMultiplier,\n            profit: profit,\n            status: 'cashed_out'\n        };\n    }\n    /**\n   * Handle multiplier update during flight\n   */ handleUpdateMultiplier(gameState, payload) {\n        if (gameState.phase !== 'flying') {\n            return gameState;\n        }\n        const newMultiplier = this.calculateMultiplier({\n            ...gameState,\n            time_elapsed: payload.timeElapsed\n        });\n        // Check if we've hit the crash point\n        if (gameState.crash_point && newMultiplier >= gameState.crash_point) {\n            return this.handleCrash({\n                ...gameState,\n                time_elapsed: payload.timeElapsed,\n                current_multiplier: gameState.crash_point\n            });\n        }\n        // Check for auto cash out\n        if (gameState.auto_cash_out && newMultiplier >= gameState.auto_cash_out && !gameState.cashed_out) {\n            return this.handleCashOut({\n                ...gameState,\n                time_elapsed: payload.timeElapsed,\n                current_multiplier: newMultiplier\n            }, {});\n        }\n        return {\n            ...gameState,\n            time_elapsed: payload.timeElapsed,\n            current_multiplier: newMultiplier\n        };\n    }\n    /**\n   * Handle crash event\n   */ handleCrash(gameState) {\n        const profit = gameState.cashed_out ? gameState.profit : -gameState.bet_amount;\n        return {\n            ...gameState,\n            phase: 'crashed',\n            status: gameState.cashed_out ? 'cashed_out' : 'lost',\n            profit: profit,\n            current_multiplier: gameState.crash_point || gameState.current_multiplier\n        };\n    }\n    /**\n   * Handle start of flying phase\n   */ handleStartRound(gameState) {\n        return {\n            ...gameState,\n            phase: 'flying',\n            time_elapsed: 0,\n            current_multiplier: 1.0\n        };\n    }\n    /**\n   * Handle end of round (waiting phase)\n   */ handleEndRound(gameState) {\n        return {\n            ...gameState,\n            phase: 'waiting'\n        };\n    }\n    /**\n   * Generate crash point using provably fair method\n   */ generateCrashPoint(serverSeed, clientSeed) {\n        // Generate a random number between 0 and 1\n        const random = this.generateRandomFromSeeds(serverSeed, clientSeed, 0);\n        // Use inverse exponential distribution to generate crash point\n        // This creates realistic crash points with most being low but some being very high\n        const houseEdge = this.config.houseEdge;\n        const crashPoint = Math.max(1.01, (1 - houseEdge) / random);\n        // Cap at maximum multiplier\n        return Math.min(crashPoint, this.config.maxMultiplier);\n    }\n    /**\n   * Generate unique round ID\n   */ generateRoundId() {\n        return `crash_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    }\n    constructor(...args){\n        super(...args), this.gameType = 'crash', this.config = {\n            id: 'crash',\n            name: 'Crash',\n            description: 'Watch the multiplier rise and cash out before it crashes! The longer you wait, the higher the multiplier, but if you wait too long, you lose everything.',\n            icon: '🚀',\n            category: 'originals',\n            minBet: 0.01,\n            maxBet: 1000,\n            houseEdge: 0.01,\n            maxMultiplier: 1000000,\n            features: [\n                'Provably Fair',\n                'Real-time',\n                'Auto Cash Out',\n                'Live Multiplier'\n            ],\n            isActive: true,\n            isNew: true,\n            isFeatured: true\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL2xpYi9nYW1lcy9jcmFzaC9DcmFzaEdhbWVQcm92aWRlci50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUN1RTtBQUV2RTs7Q0FFQyxHQUNNLE1BQU1FLDBCQUEwQkYsK0RBQWdCQTtJQW1CckQ7O0dBRUMsR0FDRCxtQkFBMEJJLE1BQVcsRUFBVztRQUM5QyxNQUFNLEVBQUVDLFVBQVUsRUFBRUMsYUFBYSxFQUFFLEdBQUdGO1FBRXRDLElBQUksQ0FBQyxJQUFJLENBQUNHLGtCQUFrQixDQUFDRixhQUFhO1lBQ3hDLE9BQU87UUFDVDtRQUVBLHFDQUFxQztRQUNyQyxJQUFJQyxrQkFBa0JFLFdBQVc7WUFDL0IsSUFBSSxPQUFPRixrQkFBa0IsWUFBWUEsZ0JBQWdCLE1BQU07Z0JBQzdELE9BQU87WUFDVDtZQUNBLElBQUlBLGdCQUFnQixJQUFJLENBQUNHLE1BQU0sQ0FBQ0MsYUFBYSxFQUFFO2dCQUM3QyxPQUFPO1lBQ1Q7UUFDRjtRQUVBLE9BQU87SUFDVDtJQUVBOztHQUVDLEdBQ0Qsb0JBQTJCRSxTQUF5QixFQUFFUixNQUFZLEVBQVU7UUFDMUUsSUFBSVEsVUFBVUMsS0FBSyxLQUFLLFVBQVU7WUFDaEMsT0FBTztRQUNUO1FBRUEsTUFBTUMsY0FBY0YsVUFBVUcsWUFBWSxJQUFJO1FBRTlDLDBEQUEwRDtRQUMxRCx5REFBeUQ7UUFDekQsTUFBTUMsYUFBYUMsS0FBS0MsR0FBRyxDQUFDLE9BQU9KO1FBRW5DLGtDQUFrQztRQUNsQyxJQUFJRixVQUFVTyxXQUFXLElBQUlILGNBQWNKLFVBQVVPLFdBQVcsRUFBRTtZQUNoRSxPQUFPUCxVQUFVTyxXQUFXO1FBQzlCO1FBRUEsT0FBT0YsS0FBS0csS0FBSyxDQUFDSixhQUFhLE9BQU8sS0FBSyw0QkFBNEI7SUFDekU7SUFFQTs7R0FFQyxHQUNELGlCQUF3QlosTUFBVyxFQUEyQjtRQUM1RCxNQUFNLEVBQUVDLFVBQVUsRUFBRUMsYUFBYSxFQUFFZ0IsT0FBTyxFQUFFQyxXQUFXLEVBQUUsR0FBR25CO1FBRTVELE1BQU1vQixXQUFXLElBQUksQ0FBQ0Msb0JBQW9CLENBQUNILFNBQVNqQixZQUFZa0I7UUFFaEUsa0RBQWtEO1FBQ2xELE1BQU1HLGFBQWEsSUFBSSxDQUFDQyxrQkFBa0IsQ0FBQ0gsU0FBU0ksV0FBVyxFQUFHSixTQUFTRCxXQUFXO1FBRXRGLE9BQU87WUFDTCxHQUFHQyxRQUFRO1lBQ1hLLFdBQVc7WUFDWFYsYUFBYU87WUFDYnBCLGVBQWVBO1lBQ2ZPLE9BQU87WUFDUEUsY0FBYztZQUNkZSxZQUFZO1lBQ1pDLFVBQVUsSUFBSSxDQUFDQyxlQUFlO1FBQ2hDO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQWFDLGtCQUFrQnJCLFNBQXlCLEVBQUVzQixNQUFrQixFQUEyQjtRQUNyRyxJQUFJLENBQUNDLGFBQWEsQ0FBQ3ZCLFdBQVdzQjtRQUU5QixPQUFRQSxPQUFPRSxJQUFJO1lBQ2pCLEtBQUtuQyw2REFBY0EsQ0FBQ29DLFVBQVU7Z0JBQzVCLE9BQU8sSUFBSSxDQUFDQyxlQUFlLENBQUMxQixXQUFXc0IsT0FBT0ssT0FBTztZQUV2RCxLQUFLdEMsNkRBQWNBLENBQUN1QyxRQUFRO2dCQUMxQixPQUFPLElBQUksQ0FBQ0MsYUFBYSxDQUFDN0IsV0FBV3NCLE9BQU9LLE9BQU87WUFFckQsS0FBSztnQkFDSCxPQUFPLElBQUksQ0FBQ0csc0JBQXNCLENBQUM5QixXQUFXc0IsT0FBT0ssT0FBTztZQUU5RCxLQUFLO2dCQUNILE9BQU8sSUFBSSxDQUFDSSxXQUFXLENBQUMvQjtZQUUxQixLQUFLO2dCQUNILE9BQU8sSUFBSSxDQUFDZ0MsZ0JBQWdCLENBQUNoQztZQUUvQixLQUFLO2dCQUNILE9BQU8sSUFBSSxDQUFDaUMsY0FBYyxDQUFDakM7WUFFN0I7Z0JBQ0UsTUFBTSxJQUFJa0MsTUFBTSxDQUFDLHFCQUFxQixFQUFFWixPQUFPRSxJQUFJLEVBQUU7UUFDekQ7SUFDRjtJQUVBOztHQUVDLEdBQ0QsZ0JBQXdCeEIsU0FBeUIsRUFBRTJCLE9BQVksRUFBa0I7UUFDL0UsT0FBTztZQUNMLEdBQUczQixTQUFTO1lBQ1pDLE9BQU87WUFDUGtDLFFBQVE7WUFDUmhDLGNBQWM7WUFDZGlDLG9CQUFvQjtZQUNwQmxCLFlBQVk7UUFDZDtJQUNGO0lBRUE7O0dBRUMsR0FDRCxjQUFzQmxCLFNBQXlCLEVBQUUyQixPQUFZLEVBQWtCO1FBQzdFLElBQUkzQixVQUFVQyxLQUFLLEtBQUssWUFBWUQsVUFBVWtCLFVBQVUsRUFBRTtZQUN4RCxNQUFNLElBQUlnQixNQUFNO1FBQ2xCO1FBRUEsTUFBTUcsb0JBQW9CLElBQUksQ0FBQ3RDLG1CQUFtQixDQUFDQztRQUNuRCxNQUFNc0MsU0FBUyxJQUFJLENBQUNDLGVBQWUsQ0FBQ3ZDLFVBQVVQLFVBQVUsRUFBRTRDO1FBRTFELE9BQU87WUFDTCxHQUFHckMsU0FBUztZQUNaa0IsWUFBWTtZQUNac0IsYUFBYUg7WUFDYkQsb0JBQW9CQztZQUNwQkMsUUFBUUE7WUFDUkgsUUFBUTtRQUNWO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELHVCQUErQm5DLFNBQXlCLEVBQUUyQixPQUFnQyxFQUFrQjtRQUMxRyxJQUFJM0IsVUFBVUMsS0FBSyxLQUFLLFVBQVU7WUFDaEMsT0FBT0Q7UUFDVDtRQUVBLE1BQU15QyxnQkFBZ0IsSUFBSSxDQUFDMUMsbUJBQW1CLENBQUM7WUFBRSxHQUFHQyxTQUFTO1lBQUVHLGNBQWN3QixRQUFRekIsV0FBVztRQUFDO1FBRWpHLHFDQUFxQztRQUNyQyxJQUFJRixVQUFVTyxXQUFXLElBQUlrQyxpQkFBaUJ6QyxVQUFVTyxXQUFXLEVBQUU7WUFDbkUsT0FBTyxJQUFJLENBQUN3QixXQUFXLENBQUM7Z0JBQ3RCLEdBQUcvQixTQUFTO2dCQUNaRyxjQUFjd0IsUUFBUXpCLFdBQVc7Z0JBQ2pDa0Msb0JBQW9CcEMsVUFBVU8sV0FBVztZQUMzQztRQUNGO1FBRUEsMEJBQTBCO1FBQzFCLElBQUlQLFVBQVVOLGFBQWEsSUFBSStDLGlCQUFpQnpDLFVBQVVOLGFBQWEsSUFBSSxDQUFDTSxVQUFVa0IsVUFBVSxFQUFFO1lBQ2hHLE9BQU8sSUFBSSxDQUFDVyxhQUFhLENBQUM7Z0JBQ3hCLEdBQUc3QixTQUFTO2dCQUNaRyxjQUFjd0IsUUFBUXpCLFdBQVc7Z0JBQ2pDa0Msb0JBQW9CSztZQUN0QixHQUFHLENBQUM7UUFDTjtRQUVBLE9BQU87WUFDTCxHQUFHekMsU0FBUztZQUNaRyxjQUFjd0IsUUFBUXpCLFdBQVc7WUFDakNrQyxvQkFBb0JLO1FBQ3RCO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELFlBQW9CekMsU0FBeUIsRUFBa0I7UUFDN0QsTUFBTXNDLFNBQVN0QyxVQUFVa0IsVUFBVSxHQUFHbEIsVUFBVXNDLE1BQU0sR0FBRyxDQUFDdEMsVUFBVVAsVUFBVTtRQUU5RSxPQUFPO1lBQ0wsR0FBR08sU0FBUztZQUNaQyxPQUFPO1lBQ1BrQyxRQUFRbkMsVUFBVWtCLFVBQVUsR0FBRyxlQUFlO1lBQzlDb0IsUUFBUUE7WUFDUkYsb0JBQW9CcEMsVUFBVU8sV0FBVyxJQUFJUCxVQUFVb0Msa0JBQWtCO1FBQzNFO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELGlCQUF5QnBDLFNBQXlCLEVBQWtCO1FBQ2xFLE9BQU87WUFDTCxHQUFHQSxTQUFTO1lBQ1pDLE9BQU87WUFDUEUsY0FBYztZQUNkaUMsb0JBQW9CO1FBQ3RCO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELGVBQXVCcEMsU0FBeUIsRUFBa0I7UUFDaEUsT0FBTztZQUNMLEdBQUdBLFNBQVM7WUFDWkMsT0FBTztRQUNUO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELG1CQUEyQnlDLFVBQWtCLEVBQUVDLFVBQWtCLEVBQVU7UUFDekUsMkNBQTJDO1FBQzNDLE1BQU1DLFNBQVMsSUFBSSxDQUFDQyx1QkFBdUIsQ0FBQ0gsWUFBWUMsWUFBWTtRQUVwRSwrREFBK0Q7UUFDL0QsbUZBQW1GO1FBQ25GLE1BQU1HLFlBQVksSUFBSSxDQUFDakQsTUFBTSxDQUFDaUQsU0FBUztRQUN2QyxNQUFNaEMsYUFBYVQsS0FBSzBDLEdBQUcsQ0FBQyxNQUFNLENBQUMsSUFBSUQsU0FBUSxJQUFLRjtRQUVwRCw0QkFBNEI7UUFDNUIsT0FBT3ZDLEtBQUsyQyxHQUFHLENBQUNsQyxZQUFZLElBQUksQ0FBQ2pCLE1BQU0sQ0FBQ0MsYUFBYTtJQUN2RDtJQUVBOztHQUVDLEdBQ0Qsa0JBQWtDO1FBQ2hDLE9BQU8sQ0FBQyxNQUFNLEVBQUVtRCxLQUFLQyxHQUFHLEdBQUcsQ0FBQyxFQUFFN0MsS0FBS3VDLE1BQU0sR0FBR08sUUFBUSxDQUFDLElBQUlDLE1BQU0sQ0FBQyxHQUFHLElBQUk7SUFDekU7O1FBclBLLHFCQUNXQyxXQUFXLGNBRVh4RCxTQUFxQjtZQUNuQ3lELElBQUk7WUFDSkMsTUFBTTtZQUNOQyxhQUFhO1lBQ2JDLE1BQU07WUFDTkMsVUFBVTtZQUNWQyxRQUFRO1lBQ1JDLFFBQVE7WUFDUmQsV0FBVztZQUNYaEQsZUFBZTtZQUNmK0QsVUFBVTtnQkFBQztnQkFBaUI7Z0JBQWE7Z0JBQWlCO2FBQWtCO1lBQzVFQyxVQUFVO1lBQ1ZDLE9BQU87WUFDUEMsWUFBWTtRQUNkOztBQXFPRiIsInNvdXJjZXMiOlsiRTpcXDExMVxcUFJPSkVDVFxcbWluZXMtZ2FtZVxcbGliXFxnYW1lc1xcY3Jhc2hcXENyYXNoR2FtZVByb3ZpZGVyLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IENyYXNoR2FtZVN0YXRlLCBHYW1lQWN0aW9uLCBHYW1lQ29uZmlnIH0gZnJvbSAnQC90eXBlcyc7XG5pbXBvcnQgeyBCYXNlR2FtZVByb3ZpZGVyLCBHYW1lQWN0aW9uVHlwZSB9IGZyb20gJy4uL0Jhc2VHYW1lUHJvdmlkZXInO1xuXG4vKipcbiAqIENyYXNoIEdhbWUgUHJvdmlkZXIgLSBIYW5kbGVzIGNyYXNoIGdhbWUgbG9naWNcbiAqL1xuZXhwb3J0IGNsYXNzIENyYXNoR2FtZVByb3ZpZGVyIGV4dGVuZHMgQmFzZUdhbWVQcm92aWRlcjxDcmFzaEdhbWVTdGF0ZT4ge1xuICBwdWJsaWMgcmVhZG9ubHkgZ2FtZVR5cGUgPSAnY3Jhc2gnIGFzIGNvbnN0O1xuICBcbiAgcHVibGljIHJlYWRvbmx5IGNvbmZpZzogR2FtZUNvbmZpZyA9IHtcbiAgICBpZDogJ2NyYXNoJyxcbiAgICBuYW1lOiAnQ3Jhc2gnLFxuICAgIGRlc2NyaXB0aW9uOiAnV2F0Y2ggdGhlIG11bHRpcGxpZXIgcmlzZSBhbmQgY2FzaCBvdXQgYmVmb3JlIGl0IGNyYXNoZXMhIFRoZSBsb25nZXIgeW91IHdhaXQsIHRoZSBoaWdoZXIgdGhlIG11bHRpcGxpZXIsIGJ1dCBpZiB5b3Ugd2FpdCB0b28gbG9uZywgeW91IGxvc2UgZXZlcnl0aGluZy4nLFxuICAgIGljb246ICfwn5qAJyxcbiAgICBjYXRlZ29yeTogJ29yaWdpbmFscycsXG4gICAgbWluQmV0OiAwLjAxLFxuICAgIG1heEJldDogMTAwMCxcbiAgICBob3VzZUVkZ2U6IDAuMDEsIC8vIDElXG4gICAgbWF4TXVsdGlwbGllcjogMTAwMDAwMCwgLy8gMSwwMDAsMDAweCB0aGVvcmV0aWNhbCBtYXhcbiAgICBmZWF0dXJlczogWydQcm92YWJseSBGYWlyJywgJ1JlYWwtdGltZScsICdBdXRvIENhc2ggT3V0JywgJ0xpdmUgTXVsdGlwbGllciddLFxuICAgIGlzQWN0aXZlOiB0cnVlLFxuICAgIGlzTmV3OiB0cnVlLFxuICAgIGlzRmVhdHVyZWQ6IHRydWVcbiAgfTtcblxuICAvKipcbiAgICogVmFsaWRhdGUgY3Jhc2ggZ2FtZSBwYXJhbWV0ZXJzXG4gICAqL1xuICBwdWJsaWMgdmFsaWRhdGVHYW1lUGFyYW1zKHBhcmFtczogYW55KTogYm9vbGVhbiB7XG4gICAgY29uc3QgeyBiZXRfYW1vdW50LCBhdXRvX2Nhc2hfb3V0IH0gPSBwYXJhbXM7XG4gICAgXG4gICAgaWYgKCF0aGlzLnZhbGlkYXRlQmFzZVBhcmFtcyhiZXRfYW1vdW50KSkge1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cblxuICAgIC8vIFZhbGlkYXRlIGF1dG8gY2FzaCBvdXQgaWYgcHJvdmlkZWRcbiAgICBpZiAoYXV0b19jYXNoX291dCAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICBpZiAodHlwZW9mIGF1dG9fY2FzaF9vdXQgIT09ICdudW1iZXInIHx8IGF1dG9fY2FzaF9vdXQgPCAxLjAxKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgIH1cbiAgICAgIGlmIChhdXRvX2Nhc2hfb3V0ID4gdGhpcy5jb25maWcubWF4TXVsdGlwbGllcikge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICB9XG4gICAgfVxuXG4gICAgcmV0dXJuIHRydWU7XG4gIH1cblxuICAvKipcbiAgICogQ2FsY3VsYXRlIGN1cnJlbnQgbXVsdGlwbGllciBiYXNlZCBvbiB0aW1lIGVsYXBzZWRcbiAgICovXG4gIHB1YmxpYyBjYWxjdWxhdGVNdWx0aXBsaWVyKGdhbWVTdGF0ZTogQ3Jhc2hHYW1lU3RhdGUsIHBhcmFtcz86IGFueSk6IG51bWJlciB7XG4gICAgaWYgKGdhbWVTdGF0ZS5waGFzZSAhPT0gJ2ZseWluZycpIHtcbiAgICAgIHJldHVybiAxLjA7XG4gICAgfVxuXG4gICAgY29uc3QgdGltZUVsYXBzZWQgPSBnYW1lU3RhdGUudGltZV9lbGFwc2VkIHx8IDA7XG4gICAgXG4gICAgLy8gVXNlIGV4cG9uZW50aWFsIGdyb3d0aDogbXVsdGlwbGllciA9IDEuMDAyXih0aW1lX2luX21zKVxuICAgIC8vIFRoaXMgY3JlYXRlcyBhIHNtb290aCBjdXJ2ZSB0aGF0IGFjY2VsZXJhdGVzIG92ZXIgdGltZVxuICAgIGNvbnN0IG11bHRpcGxpZXIgPSBNYXRoLnBvdygxLjAwMiwgdGltZUVsYXBzZWQpO1xuICAgIFxuICAgIC8vIENhcCBhdCBjcmFzaCBwb2ludCBpZiBpdCBleGlzdHNcbiAgICBpZiAoZ2FtZVN0YXRlLmNyYXNoX3BvaW50ICYmIG11bHRpcGxpZXIgPj0gZ2FtZVN0YXRlLmNyYXNoX3BvaW50KSB7XG4gICAgICByZXR1cm4gZ2FtZVN0YXRlLmNyYXNoX3BvaW50O1xuICAgIH1cblxuICAgIHJldHVybiBNYXRoLnJvdW5kKG11bHRpcGxpZXIgKiAxMDApIC8gMTAwOyAvLyBSb3VuZCB0byAyIGRlY2ltYWwgcGxhY2VzXG4gIH1cblxuICAvKipcbiAgICogR2VuZXJhdGUgY3Jhc2ggZ2FtZSBkYXRhXG4gICAqL1xuICBwdWJsaWMgZ2VuZXJhdGVHYW1lRGF0YShwYXJhbXM6IGFueSk6IFBhcnRpYWw8Q3Jhc2hHYW1lU3RhdGU+IHtcbiAgICBjb25zdCB7IGJldF9hbW91bnQsIGF1dG9fY2FzaF9vdXQsIHVzZXJfaWQsIGNsaWVudF9zZWVkIH0gPSBwYXJhbXM7XG4gICAgXG4gICAgY29uc3QgYmFzZURhdGEgPSB0aGlzLmdlbmVyYXRlQmFzZUdhbWVEYXRhKHVzZXJfaWQsIGJldF9hbW91bnQsIGNsaWVudF9zZWVkKTtcbiAgICBcbiAgICAvLyBHZW5lcmF0ZSBjcmFzaCBwb2ludCB1c2luZyBwcm92YWJseSBmYWlyIG1ldGhvZFxuICAgIGNvbnN0IGNyYXNoUG9pbnQgPSB0aGlzLmdlbmVyYXRlQ3Jhc2hQb2ludChiYXNlRGF0YS5zZXJ2ZXJfc2VlZCEsIGJhc2VEYXRhLmNsaWVudF9zZWVkISk7XG4gICAgXG4gICAgcmV0dXJuIHtcbiAgICAgIC4uLmJhc2VEYXRhLFxuICAgICAgZ2FtZV90eXBlOiAnY3Jhc2gnLFxuICAgICAgY3Jhc2hfcG9pbnQ6IGNyYXNoUG9pbnQsXG4gICAgICBhdXRvX2Nhc2hfb3V0OiBhdXRvX2Nhc2hfb3V0LFxuICAgICAgcGhhc2U6ICdiZXR0aW5nJyxcbiAgICAgIHRpbWVfZWxhcHNlZDogMCxcbiAgICAgIGNhc2hlZF9vdXQ6IGZhbHNlLFxuICAgICAgcm91bmRfaWQ6IHRoaXMuZ2VuZXJhdGVSb3VuZElkKClcbiAgICB9O1xuICB9XG5cbiAgLyoqXG4gICAqIFByb2Nlc3MgY3Jhc2ggZ2FtZSBhY3Rpb25zXG4gICAqL1xuICBwdWJsaWMgYXN5bmMgcHJvY2Vzc0dhbWVBY3Rpb24oZ2FtZVN0YXRlOiBDcmFzaEdhbWVTdGF0ZSwgYWN0aW9uOiBHYW1lQWN0aW9uKTogUHJvbWlzZTxDcmFzaEdhbWVTdGF0ZT4ge1xuICAgIHRoaXMubG9nR2FtZUFjdGlvbihnYW1lU3RhdGUsIGFjdGlvbik7XG5cbiAgICBzd2l0Y2ggKGFjdGlvbi50eXBlKSB7XG4gICAgICBjYXNlIEdhbWVBY3Rpb25UeXBlLlNUQVJUX0dBTUU6XG4gICAgICAgIHJldHVybiB0aGlzLmhhbmRsZVN0YXJ0R2FtZShnYW1lU3RhdGUsIGFjdGlvbi5wYXlsb2FkKTtcbiAgICAgIFxuICAgICAgY2FzZSBHYW1lQWN0aW9uVHlwZS5DQVNIX09VVDpcbiAgICAgICAgcmV0dXJuIHRoaXMuaGFuZGxlQ2FzaE91dChnYW1lU3RhdGUsIGFjdGlvbi5wYXlsb2FkKTtcbiAgICAgIFxuICAgICAgY2FzZSAnVVBEQVRFX01VTFRJUExJRVInOlxuICAgICAgICByZXR1cm4gdGhpcy5oYW5kbGVVcGRhdGVNdWx0aXBsaWVyKGdhbWVTdGF0ZSwgYWN0aW9uLnBheWxvYWQpO1xuICAgICAgXG4gICAgICBjYXNlICdDUkFTSCc6XG4gICAgICAgIHJldHVybiB0aGlzLmhhbmRsZUNyYXNoKGdhbWVTdGF0ZSk7XG4gICAgICBcbiAgICAgIGNhc2UgJ1NUQVJUX1JPVU5EJzpcbiAgICAgICAgcmV0dXJuIHRoaXMuaGFuZGxlU3RhcnRSb3VuZChnYW1lU3RhdGUpO1xuICAgICAgXG4gICAgICBjYXNlICdFTkRfUk9VTkQnOlxuICAgICAgICByZXR1cm4gdGhpcy5oYW5kbGVFbmRSb3VuZChnYW1lU3RhdGUpO1xuICAgICAgXG4gICAgICBkZWZhdWx0OlxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYFVua25vd24gYWN0aW9uIHR5cGU6ICR7YWN0aW9uLnR5cGV9YCk7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIEhhbmRsZSBzdGFydGluZyB0aGUgZ2FtZSAoYmV0dGluZyBwaGFzZSlcbiAgICovXG4gIHByaXZhdGUgaGFuZGxlU3RhcnRHYW1lKGdhbWVTdGF0ZTogQ3Jhc2hHYW1lU3RhdGUsIHBheWxvYWQ6IGFueSk6IENyYXNoR2FtZVN0YXRlIHtcbiAgICByZXR1cm4ge1xuICAgICAgLi4uZ2FtZVN0YXRlLFxuICAgICAgcGhhc2U6ICdiZXR0aW5nJyxcbiAgICAgIHN0YXR1czogJ2FjdGl2ZScsXG4gICAgICB0aW1lX2VsYXBzZWQ6IDAsXG4gICAgICBjdXJyZW50X211bHRpcGxpZXI6IDEuMCxcbiAgICAgIGNhc2hlZF9vdXQ6IGZhbHNlXG4gICAgfTtcbiAgfVxuXG4gIC8qKlxuICAgKiBIYW5kbGUgY2FzaCBvdXQgYWN0aW9uXG4gICAqL1xuICBwcml2YXRlIGhhbmRsZUNhc2hPdXQoZ2FtZVN0YXRlOiBDcmFzaEdhbWVTdGF0ZSwgcGF5bG9hZDogYW55KTogQ3Jhc2hHYW1lU3RhdGUge1xuICAgIGlmIChnYW1lU3RhdGUucGhhc2UgIT09ICdmbHlpbmcnIHx8IGdhbWVTdGF0ZS5jYXNoZWRfb3V0KSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0Nhbm5vdCBjYXNoIG91dCBhdCB0aGlzIHRpbWUnKTtcbiAgICB9XG5cbiAgICBjb25zdCBjdXJyZW50TXVsdGlwbGllciA9IHRoaXMuY2FsY3VsYXRlTXVsdGlwbGllcihnYW1lU3RhdGUpO1xuICAgIGNvbnN0IHByb2ZpdCA9IHRoaXMuY2FsY3VsYXRlUHJvZml0KGdhbWVTdGF0ZS5iZXRfYW1vdW50LCBjdXJyZW50TXVsdGlwbGllcik7XG5cbiAgICByZXR1cm4ge1xuICAgICAgLi4uZ2FtZVN0YXRlLFxuICAgICAgY2FzaGVkX291dDogdHJ1ZSxcbiAgICAgIGNhc2hfb3V0X2F0OiBjdXJyZW50TXVsdGlwbGllcixcbiAgICAgIGN1cnJlbnRfbXVsdGlwbGllcjogY3VycmVudE11bHRpcGxpZXIsXG4gICAgICBwcm9maXQ6IHByb2ZpdCxcbiAgICAgIHN0YXR1czogJ2Nhc2hlZF9vdXQnXG4gICAgfTtcbiAgfVxuXG4gIC8qKlxuICAgKiBIYW5kbGUgbXVsdGlwbGllciB1cGRhdGUgZHVyaW5nIGZsaWdodFxuICAgKi9cbiAgcHJpdmF0ZSBoYW5kbGVVcGRhdGVNdWx0aXBsaWVyKGdhbWVTdGF0ZTogQ3Jhc2hHYW1lU3RhdGUsIHBheWxvYWQ6IHsgdGltZUVsYXBzZWQ6IG51bWJlciB9KTogQ3Jhc2hHYW1lU3RhdGUge1xuICAgIGlmIChnYW1lU3RhdGUucGhhc2UgIT09ICdmbHlpbmcnKSB7XG4gICAgICByZXR1cm4gZ2FtZVN0YXRlO1xuICAgIH1cblxuICAgIGNvbnN0IG5ld011bHRpcGxpZXIgPSB0aGlzLmNhbGN1bGF0ZU11bHRpcGxpZXIoeyAuLi5nYW1lU3RhdGUsIHRpbWVfZWxhcHNlZDogcGF5bG9hZC50aW1lRWxhcHNlZCB9KTtcbiAgICBcbiAgICAvLyBDaGVjayBpZiB3ZSd2ZSBoaXQgdGhlIGNyYXNoIHBvaW50XG4gICAgaWYgKGdhbWVTdGF0ZS5jcmFzaF9wb2ludCAmJiBuZXdNdWx0aXBsaWVyID49IGdhbWVTdGF0ZS5jcmFzaF9wb2ludCkge1xuICAgICAgcmV0dXJuIHRoaXMuaGFuZGxlQ3Jhc2goe1xuICAgICAgICAuLi5nYW1lU3RhdGUsXG4gICAgICAgIHRpbWVfZWxhcHNlZDogcGF5bG9hZC50aW1lRWxhcHNlZCxcbiAgICAgICAgY3VycmVudF9tdWx0aXBsaWVyOiBnYW1lU3RhdGUuY3Jhc2hfcG9pbnRcbiAgICAgIH0pO1xuICAgIH1cblxuICAgIC8vIENoZWNrIGZvciBhdXRvIGNhc2ggb3V0XG4gICAgaWYgKGdhbWVTdGF0ZS5hdXRvX2Nhc2hfb3V0ICYmIG5ld011bHRpcGxpZXIgPj0gZ2FtZVN0YXRlLmF1dG9fY2FzaF9vdXQgJiYgIWdhbWVTdGF0ZS5jYXNoZWRfb3V0KSB7XG4gICAgICByZXR1cm4gdGhpcy5oYW5kbGVDYXNoT3V0KHtcbiAgICAgICAgLi4uZ2FtZVN0YXRlLFxuICAgICAgICB0aW1lX2VsYXBzZWQ6IHBheWxvYWQudGltZUVsYXBzZWQsXG4gICAgICAgIGN1cnJlbnRfbXVsdGlwbGllcjogbmV3TXVsdGlwbGllclxuICAgICAgfSwge30pO1xuICAgIH1cblxuICAgIHJldHVybiB7XG4gICAgICAuLi5nYW1lU3RhdGUsXG4gICAgICB0aW1lX2VsYXBzZWQ6IHBheWxvYWQudGltZUVsYXBzZWQsXG4gICAgICBjdXJyZW50X211bHRpcGxpZXI6IG5ld011bHRpcGxpZXJcbiAgICB9O1xuICB9XG5cbiAgLyoqXG4gICAqIEhhbmRsZSBjcmFzaCBldmVudFxuICAgKi9cbiAgcHJpdmF0ZSBoYW5kbGVDcmFzaChnYW1lU3RhdGU6IENyYXNoR2FtZVN0YXRlKTogQ3Jhc2hHYW1lU3RhdGUge1xuICAgIGNvbnN0IHByb2ZpdCA9IGdhbWVTdGF0ZS5jYXNoZWRfb3V0ID8gZ2FtZVN0YXRlLnByb2ZpdCA6IC1nYW1lU3RhdGUuYmV0X2Ftb3VudDtcbiAgICBcbiAgICByZXR1cm4ge1xuICAgICAgLi4uZ2FtZVN0YXRlLFxuICAgICAgcGhhc2U6ICdjcmFzaGVkJyxcbiAgICAgIHN0YXR1czogZ2FtZVN0YXRlLmNhc2hlZF9vdXQgPyAnY2FzaGVkX291dCcgOiAnbG9zdCcsXG4gICAgICBwcm9maXQ6IHByb2ZpdCxcbiAgICAgIGN1cnJlbnRfbXVsdGlwbGllcjogZ2FtZVN0YXRlLmNyYXNoX3BvaW50IHx8IGdhbWVTdGF0ZS5jdXJyZW50X211bHRpcGxpZXJcbiAgICB9O1xuICB9XG5cbiAgLyoqXG4gICAqIEhhbmRsZSBzdGFydCBvZiBmbHlpbmcgcGhhc2VcbiAgICovXG4gIHByaXZhdGUgaGFuZGxlU3RhcnRSb3VuZChnYW1lU3RhdGU6IENyYXNoR2FtZVN0YXRlKTogQ3Jhc2hHYW1lU3RhdGUge1xuICAgIHJldHVybiB7XG4gICAgICAuLi5nYW1lU3RhdGUsXG4gICAgICBwaGFzZTogJ2ZseWluZycsXG4gICAgICB0aW1lX2VsYXBzZWQ6IDAsXG4gICAgICBjdXJyZW50X211bHRpcGxpZXI6IDEuMFxuICAgIH07XG4gIH1cblxuICAvKipcbiAgICogSGFuZGxlIGVuZCBvZiByb3VuZCAod2FpdGluZyBwaGFzZSlcbiAgICovXG4gIHByaXZhdGUgaGFuZGxlRW5kUm91bmQoZ2FtZVN0YXRlOiBDcmFzaEdhbWVTdGF0ZSk6IENyYXNoR2FtZVN0YXRlIHtcbiAgICByZXR1cm4ge1xuICAgICAgLi4uZ2FtZVN0YXRlLFxuICAgICAgcGhhc2U6ICd3YWl0aW5nJ1xuICAgIH07XG4gIH1cblxuICAvKipcbiAgICogR2VuZXJhdGUgY3Jhc2ggcG9pbnQgdXNpbmcgcHJvdmFibHkgZmFpciBtZXRob2RcbiAgICovXG4gIHByaXZhdGUgZ2VuZXJhdGVDcmFzaFBvaW50KHNlcnZlclNlZWQ6IHN0cmluZywgY2xpZW50U2VlZDogc3RyaW5nKTogbnVtYmVyIHtcbiAgICAvLyBHZW5lcmF0ZSBhIHJhbmRvbSBudW1iZXIgYmV0d2VlbiAwIGFuZCAxXG4gICAgY29uc3QgcmFuZG9tID0gdGhpcy5nZW5lcmF0ZVJhbmRvbUZyb21TZWVkcyhzZXJ2ZXJTZWVkLCBjbGllbnRTZWVkLCAwKTtcbiAgICBcbiAgICAvLyBVc2UgaW52ZXJzZSBleHBvbmVudGlhbCBkaXN0cmlidXRpb24gdG8gZ2VuZXJhdGUgY3Jhc2ggcG9pbnRcbiAgICAvLyBUaGlzIGNyZWF0ZXMgcmVhbGlzdGljIGNyYXNoIHBvaW50cyB3aXRoIG1vc3QgYmVpbmcgbG93IGJ1dCBzb21lIGJlaW5nIHZlcnkgaGlnaFxuICAgIGNvbnN0IGhvdXNlRWRnZSA9IHRoaXMuY29uZmlnLmhvdXNlRWRnZTtcbiAgICBjb25zdCBjcmFzaFBvaW50ID0gTWF0aC5tYXgoMS4wMSwgKDEgLSBob3VzZUVkZ2UpIC8gcmFuZG9tKTtcbiAgICBcbiAgICAvLyBDYXAgYXQgbWF4aW11bSBtdWx0aXBsaWVyXG4gICAgcmV0dXJuIE1hdGgubWluKGNyYXNoUG9pbnQsIHRoaXMuY29uZmlnLm1heE11bHRpcGxpZXIpO1xuICB9XG5cbiAgLyoqXG4gICAqIEdlbmVyYXRlIHVuaXF1ZSByb3VuZCBJRFxuICAgKi9cbiAgcHJpdmF0ZSBnZW5lcmF0ZVJvdW5kSWQoKTogc3RyaW5nIHtcbiAgICByZXR1cm4gYGNyYXNoXyR7RGF0ZS5ub3coKX1fJHtNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHIoMiwgOSl9YDtcbiAgfVxufVxuIl0sIm5hbWVzIjpbIkJhc2VHYW1lUHJvdmlkZXIiLCJHYW1lQWN0aW9uVHlwZSIsIkNyYXNoR2FtZVByb3ZpZGVyIiwidmFsaWRhdGVHYW1lUGFyYW1zIiwicGFyYW1zIiwiYmV0X2Ftb3VudCIsImF1dG9fY2FzaF9vdXQiLCJ2YWxpZGF0ZUJhc2VQYXJhbXMiLCJ1bmRlZmluZWQiLCJjb25maWciLCJtYXhNdWx0aXBsaWVyIiwiY2FsY3VsYXRlTXVsdGlwbGllciIsImdhbWVTdGF0ZSIsInBoYXNlIiwidGltZUVsYXBzZWQiLCJ0aW1lX2VsYXBzZWQiLCJtdWx0aXBsaWVyIiwiTWF0aCIsInBvdyIsImNyYXNoX3BvaW50Iiwicm91bmQiLCJnZW5lcmF0ZUdhbWVEYXRhIiwidXNlcl9pZCIsImNsaWVudF9zZWVkIiwiYmFzZURhdGEiLCJnZW5lcmF0ZUJhc2VHYW1lRGF0YSIsImNyYXNoUG9pbnQiLCJnZW5lcmF0ZUNyYXNoUG9pbnQiLCJzZXJ2ZXJfc2VlZCIsImdhbWVfdHlwZSIsImNhc2hlZF9vdXQiLCJyb3VuZF9pZCIsImdlbmVyYXRlUm91bmRJZCIsInByb2Nlc3NHYW1lQWN0aW9uIiwiYWN0aW9uIiwibG9nR2FtZUFjdGlvbiIsInR5cGUiLCJTVEFSVF9HQU1FIiwiaGFuZGxlU3RhcnRHYW1lIiwicGF5bG9hZCIsIkNBU0hfT1VUIiwiaGFuZGxlQ2FzaE91dCIsImhhbmRsZVVwZGF0ZU11bHRpcGxpZXIiLCJoYW5kbGVDcmFzaCIsImhhbmRsZVN0YXJ0Um91bmQiLCJoYW5kbGVFbmRSb3VuZCIsIkVycm9yIiwic3RhdHVzIiwiY3VycmVudF9tdWx0aXBsaWVyIiwiY3VycmVudE11bHRpcGxpZXIiLCJwcm9maXQiLCJjYWxjdWxhdGVQcm9maXQiLCJjYXNoX291dF9hdCIsIm5ld011bHRpcGxpZXIiLCJzZXJ2ZXJTZWVkIiwiY2xpZW50U2VlZCIsInJhbmRvbSIsImdlbmVyYXRlUmFuZG9tRnJvbVNlZWRzIiwiaG91c2VFZGdlIiwibWF4IiwibWluIiwiRGF0ZSIsIm5vdyIsInRvU3RyaW5nIiwic3Vic3RyIiwiZ2FtZVR5cGUiLCJpZCIsIm5hbWUiLCJkZXNjcmlwdGlvbiIsImljb24iLCJjYXRlZ29yeSIsIm1pbkJldCIsIm1heEJldCIsImZlYXR1cmVzIiwiaXNBY3RpdmUiLCJpc05ldyIsImlzRmVhdHVyZWQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./lib/games/crash/CrashGameProvider.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./lib/games/dice/DiceGameProvider.ts":
/*!********************************************!*\
  !*** ./lib/games/dice/DiceGameProvider.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DiceGameProvider: () => (/* binding */ DiceGameProvider)\n/* harmony export */ });\n/* harmony import */ var _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../BaseGameProvider */ \"(pages-dir-node)/./lib/games/BaseGameProvider.ts\");\n\n/**\n * Dice Game Provider - Implements the classic dice gambling game\n * Players bet on whether a dice roll (1-100) will be over or under their chosen target number\n */ class DiceGameProvider extends _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__.BaseGameProvider {\n    /**\n   * Validate dice game parameters\n   */ validateGameParams(params) {\n        const { betAmount, targetNumber, rollUnder } = params;\n        // Validate base parameters\n        if (!this.validateBaseParams(betAmount)) {\n            return false;\n        }\n        // Validate target number\n        if (typeof targetNumber !== 'number' || targetNumber < this.MIN_TARGET || targetNumber > this.MAX_TARGET) {\n            return false;\n        }\n        // Validate roll direction\n        if (typeof rollUnder !== 'boolean') {\n            return false;\n        }\n        return true;\n    }\n    /**\n   * Calculate multiplier based on win chance\n   */ calculateMultiplier(gameState, params) {\n        const { target_number, roll_under } = gameState;\n        // Calculate win chance\n        const winChance = this.calculateWinChance(target_number, roll_under);\n        // Calculate multiplier: (100 - house_edge) / win_chance\n        const multiplier = (100 - this.config.houseEdge * 100) / winChance;\n        // Round to 4 decimal places and ensure minimum of 1.01\n        return Math.max(1.01, Math.round(multiplier * 10000) / 10000);\n    }\n    /**\n   * Calculate win chance percentage\n   */ calculateWinChance(targetNumber, rollUnder) {\n        if (rollUnder) {\n            // Win if roll < target (1 to target-1)\n            return targetNumber - 1;\n        } else {\n            // Win if roll > target (target+1 to 100)\n            return 100 - targetNumber;\n        }\n    }\n    /**\n   * Generate dice game data\n   */ generateGameData(params) {\n        const { userId, betAmount, targetNumber, rollUnder, clientSeed } = params;\n        if (!this.validateGameParams({\n            betAmount,\n            targetNumber,\n            rollUnder\n        })) {\n            throw new _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__.InvalidGameParamsError(this.gameType);\n        }\n        const baseData = this.generateBaseGameData(userId, betAmount, clientSeed);\n        const multiplier = this.calculateMultiplier({\n            ...baseData,\n            game_type: 'dice',\n            target_number: targetNumber,\n            roll_under: rollUnder\n        });\n        return {\n            ...baseData,\n            game_type: 'dice',\n            target_number: targetNumber,\n            roll_under: rollUnder,\n            current_multiplier: multiplier,\n            result: undefined // Will be set when dice is rolled\n        };\n    }\n    /**\n   * Process dice game actions\n   */ async processGameAction(gameState, action) {\n        switch(action.type){\n            case _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__.GameActionType.MAKE_MOVE:\n                return this.rollDice(gameState);\n            default:\n                throw new _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__.GameActionError(`Unsupported action type: ${action.type}`, this.gameType);\n        }\n    }\n    /**\n   * Roll the dice and determine outcome\n   */ rollDice(gameState) {\n        if (gameState.status !== 'active') {\n            throw new _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__.GameActionError('Game is not active', this.gameType);\n        }\n        if (gameState.result !== undefined) {\n            throw new _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__.GameActionError('Dice already rolled', this.gameType);\n        }\n        // Generate dice result using provably fair method\n        const diceResult = this.generateRandomInt(this.DICE_MIN, this.DICE_MAX, gameState.server_seed, gameState.client_seed, 0 // nonce for dice roll\n        );\n        // Determine if player won\n        const won = this.checkWin(diceResult, gameState.target_number, gameState.roll_under);\n        // Calculate profit\n        const profit = won ? gameState.bet_amount * (gameState.current_multiplier - 1) : -gameState.bet_amount;\n        return {\n            ...gameState,\n            result: diceResult,\n            status: won ? 'won' : 'lost',\n            profit: profit,\n            updated_at: new Date().toISOString()\n        };\n    }\n    /**\n   * Check if the dice result is a win\n   */ checkWin(diceResult, targetNumber, rollUnder) {\n        if (rollUnder) {\n            return diceResult < targetNumber;\n        } else {\n            return diceResult > targetNumber;\n        }\n    }\n    /**\n   * Get game statistics for display\n   */ getGameStats(gameState) {\n        const winChance = this.calculateWinChance(gameState.target_number, gameState.roll_under);\n        return {\n            targetNumber: gameState.target_number,\n            rollUnder: gameState.roll_under,\n            winChance: winChance,\n            multiplier: gameState.current_multiplier,\n            result: gameState.result,\n            profit: gameState.profit,\n            status: gameState.status\n        };\n    }\n    constructor(...args){\n        super(...args), this.gameType = 'dice', this.config = {\n            id: 'dice',\n            name: 'Dice',\n            description: 'Roll the dice and predict if the result will be over or under your chosen number. Simple yet thrilling!',\n            icon: '🎲',\n            category: 'originals',\n            minBet: 0.01,\n            maxBet: 1000,\n            houseEdge: 0.01,\n            maxMultiplier: 9900,\n            features: [\n                'Provably Fair',\n                'Instant Play',\n                'Custom Multiplier'\n            ],\n            isActive: true,\n            isFeatured: true,\n            isNew: true\n        }, // Dice game constants\n        this.MIN_TARGET = 2, this.MAX_TARGET = 98, this.DICE_MIN = 1, this.DICE_MAX = 100;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./lib/games/dice/DiceGameProvider.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./lib/games/mines/MinesGameProvider.ts":
/*!**********************************************!*\
  !*** ./lib/games/mines/MinesGameProvider.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MinesGameProvider: () => (/* binding */ MinesGameProvider)\n/* harmony export */ });\n/* harmony import */ var _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../BaseGameProvider */ \"(pages-dir-node)/./lib/games/BaseGameProvider.ts\");\n\n/**\n * Mines Game Provider - Implements the classic minesweeper-style gambling game\n */ class MinesGameProvider extends _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__.BaseGameProvider {\n    /**\n   * Validate mines game parameters\n   */ validateGameParams(params) {\n        const { betAmount, mineCount } = params;\n        // Validate base parameters\n        if (!this.validateBaseParams(betAmount)) {\n            return false;\n        }\n        // Validate mine count\n        if (typeof mineCount !== 'number' || mineCount < this.MIN_MINES || mineCount > this.MAX_MINES) {\n            return false;\n        }\n        return true;\n    }\n    /**\n   * Calculate multiplier based on mines and revealed safe cells\n   */ calculateMultiplier(gameState, params) {\n        const revealedSafeCells = params?.revealedCells ?? gameState.revealed_cells.length;\n        if (revealedSafeCells === 0) return 1.0;\n        const totalSafeCells = this.GRID_SIZE - gameState.mine_count;\n        const remainingSafeCells = totalSafeCells - revealedSafeCells;\n        if (remainingSafeCells <= 0) return 1.0;\n        // Calculate probability-based multiplier\n        let multiplier = 1.0;\n        for(let i = 0; i < revealedSafeCells; i++){\n            const safeCellsAtStep = totalSafeCells - i;\n            const totalCellsAtStep = this.GRID_SIZE - i;\n            const probability = safeCellsAtStep / totalCellsAtStep;\n            multiplier *= 1 / probability;\n        }\n        // Apply house edge\n        multiplier = this.applyHouseEdge(multiplier);\n        // Ensure multiplier doesn't exceed maximum\n        return Math.min(multiplier, this.config.maxMultiplier);\n    }\n    /**\n   * Generate mines game data\n   */ generateGameData(params) {\n        const { userId, betAmount, mineCount, clientSeed } = params;\n        if (!this.validateGameParams({\n            betAmount,\n            mineCount\n        })) {\n            throw new _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__.InvalidGameParamsError(this.gameType);\n        }\n        const baseData = this.generateBaseGameData(userId, betAmount, clientSeed);\n        const minePositions = this.generateMinePositions(baseData.server_seed, baseData.client_seed, mineCount);\n        return {\n            ...baseData,\n            game_type: 'mines',\n            grid_size: this.GRID_SIZE,\n            mine_count: mineCount,\n            revealed_cells: [],\n            mine_positions: minePositions\n        };\n    }\n    /**\n   * Process game actions (reveal cell, cash out, etc.)\n   */ async processGameAction(gameState, action) {\n        this.logGameAction(gameState, action);\n        if (!this.isGameActive(gameState)) {\n            throw new _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__.GameNotActiveError(this.gameType);\n        }\n        switch(action.type){\n            case _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__.GameActionType.MAKE_MOVE:\n                return this.processRevealCell(gameState, action.payload.cellIndex);\n            case _BaseGameProvider__WEBPACK_IMPORTED_MODULE_0__.GameActionType.CASH_OUT:\n                return this.processCashOut(gameState);\n            default:\n                throw new Error(`Unknown action type: ${action.type}`);\n        }\n    }\n    /**\n   * Process revealing a cell\n   */ processRevealCell(gameState, cellIndex) {\n        // Validate cell index\n        if (cellIndex < 0 || cellIndex >= this.GRID_SIZE) {\n            throw new Error('Invalid cell index');\n        }\n        // Check if cell is already revealed\n        if (gameState.revealed_cells.includes(cellIndex)) {\n            throw new Error('Cell already revealed');\n        }\n        const newRevealedCells = [\n            ...gameState.revealed_cells,\n            cellIndex\n        ];\n        const hitMine = gameState.mine_positions.includes(cellIndex);\n        if (hitMine) {\n            // Game over - hit mine\n            return {\n                ...gameState,\n                revealed_cells: newRevealedCells,\n                status: 'lost',\n                profit: -gameState.bet_amount,\n                current_multiplier: 0\n            };\n        } else {\n            // Safe cell revealed\n            const newMultiplier = this.calculateMultiplier(gameState, {\n                revealedCells: newRevealedCells.length\n            });\n            const profit = this.calculateProfit(gameState.bet_amount, newMultiplier);\n            // Check if all safe cells are revealed (auto win)\n            const totalSafeCells = this.GRID_SIZE - gameState.mine_count;\n            const isGameComplete = newRevealedCells.length === totalSafeCells;\n            return {\n                ...gameState,\n                revealed_cells: newRevealedCells,\n                current_multiplier: newMultiplier,\n                profit: profit,\n                status: isGameComplete ? 'won' : 'active'\n            };\n        }\n    }\n    /**\n   * Process cash out action\n   */ processCashOut(gameState) {\n        if (gameState.revealed_cells.length === 0) {\n            throw new Error('Cannot cash out without revealing any cells');\n        }\n        const profit = this.calculateProfit(gameState.bet_amount, gameState.current_multiplier);\n        return {\n            ...gameState,\n            status: 'cashed_out',\n            profit: profit\n        };\n    }\n    /**\n   * Generate mine positions using provably fair method\n   */ generateMinePositions(serverSeed, clientSeed, mineCount) {\n        const positions = [];\n        const availablePositions = Array.from({\n            length: this.GRID_SIZE\n        }, (_, i)=>i);\n        // Shuffle available positions using provably fair randomness\n        const shuffledPositions = this.shuffleArray(availablePositions, serverSeed, clientSeed);\n        // Take first mineCount positions\n        return shuffledPositions.slice(0, mineCount).sort((a, b)=>a - b);\n    }\n    /**\n   * Get safe cells remaining\n   */ getSafeCellsRemaining(gameState) {\n        const totalSafeCells = this.GRID_SIZE - gameState.mine_count;\n        return totalSafeCells - gameState.revealed_cells.length;\n    }\n    /**\n   * Get next multiplier if safe cell is revealed\n   */ getNextMultiplier(gameState) {\n        if (!this.isGameActive(gameState)) return gameState.current_multiplier;\n        const nextRevealedCount = gameState.revealed_cells.length + 1;\n        return this.calculateMultiplier(gameState, {\n            revealedCells: nextRevealedCount\n        });\n    }\n    /**\n   * Check if player can cash out\n   */ canCashOut(gameState) {\n        return this.isGameActive(gameState) && gameState.revealed_cells.length > 0;\n    }\n    constructor(...args){\n        super(...args), this.gameType = 'mines', this.config = {\n            id: 'mines',\n            name: 'Mines',\n            description: 'Click tiles to reveal gems while avoiding hidden mines. Cash out anytime to secure your winnings!',\n            icon: '💎',\n            category: 'originals',\n            minBet: 0.01,\n            maxBet: 1000,\n            houseEdge: 0.04,\n            maxMultiplier: 1000,\n            features: [\n                'Provably Fair',\n                'Instant Play',\n                'Auto Cashout',\n                'Custom Risk'\n            ],\n            isActive: true,\n            isFeatured: true,\n            isNew: false\n        }, this.GRID_SIZE = 25, this.MIN_MINES = 1, this.MAX_MINES = 24;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./lib/games/mines/MinesGameProvider.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./lib/games/registry.ts":
/*!*******************************!*\
  !*** ./lib/games/registry.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GameRegistry: () => (/* binding */ GameRegistry),\n/* harmony export */   gameRegistry: () => (/* binding */ gameRegistry)\n/* harmony export */ });\n/**\n * Game Registry - Central hub for all game configurations and providers\n */ class GameRegistry {\n    /**\n   * Register a new game with its configuration and provider\n   */ registerGame(config, provider) {\n        if (this.games.has(config.id)) {\n            console.warn(`Game ${config.id} is already registered. Overwriting...`);\n        }\n        this.games.set(config.id, config);\n        this.providers.set(config.id, provider);\n        console.log(`✅ Registered game: ${config.name} (${config.id})`);\n    }\n    /**\n   * Get game configuration by type\n   */ getGameConfig(gameType) {\n        return this.games.get(gameType);\n    }\n    /**\n   * Get game provider by type\n   */ getGameProvider(gameType) {\n        return this.providers.get(gameType);\n    }\n    /**\n   * Get all registered games\n   */ getAllGames() {\n        return Array.from(this.games.values());\n    }\n    /**\n   * Get games by category\n   */ getGamesByCategory(category) {\n        return this.getAllGames().filter((game)=>game.category === category);\n    }\n    /**\n   * Get active games only\n   */ getActiveGames() {\n        return this.getAllGames().filter((game)=>game.isActive);\n    }\n    /**\n   * Get featured games\n   */ getFeaturedGames() {\n        return this.getAllGames().filter((game)=>game.isFeatured && game.isActive);\n    }\n    /**\n   * Get new games\n   */ getNewGames() {\n        return this.getAllGames().filter((game)=>game.isNew && game.isActive);\n    }\n    /**\n   * Search games by name or description\n   */ searchGames(query) {\n        const lowercaseQuery = query.toLowerCase();\n        return this.getAllGames().filter((game)=>game.name.toLowerCase().includes(lowercaseQuery) || game.description.toLowerCase().includes(lowercaseQuery) || game.features.some((feature)=>feature.toLowerCase().includes(lowercaseQuery)));\n    }\n    /**\n   * Check if a game type is registered\n   */ isGameRegistered(gameType) {\n        return this.games.has(gameType);\n    }\n    /**\n   * Get game statistics\n   */ getRegistryStats() {\n        const allGames = this.getAllGames();\n        const activeGames = this.getActiveGames();\n        const gamesByCategory = allGames.reduce((acc, game)=>{\n            acc[game.category] = (acc[game.category] || 0) + 1;\n            return acc;\n        }, {});\n        return {\n            totalGames: allGames.length,\n            activeGames: activeGames.length,\n            gamesByCategory\n        };\n    }\n    /**\n   * Validate game configuration\n   */ validateGameConfig(config) {\n        const requiredFields = [\n            'id',\n            'name',\n            'description',\n            'category',\n            'minBet',\n            'maxBet'\n        ];\n        for (const field of requiredFields){\n            if (!(field in config)) {\n                console.error(`Game config missing required field: ${field}`);\n                return false;\n            }\n        }\n        if (config.minBet >= config.maxBet) {\n            console.error('minBet must be less than maxBet');\n            return false;\n        }\n        if (config.houseEdge < 0 || config.houseEdge > 1) {\n            console.error('houseEdge must be between 0 and 1');\n            return false;\n        }\n        return true;\n    }\n    /**\n   * Unregister a game (for testing or maintenance)\n   */ unregisterGame(gameType) {\n        const hasGame = this.games.has(gameType);\n        this.games.delete(gameType);\n        this.providers.delete(gameType);\n        if (hasGame) {\n            console.log(`🗑️ Unregistered game: ${gameType}`);\n        }\n        return hasGame;\n    }\n    /**\n   * Clear all registered games (for testing)\n   */ clear() {\n        this.games.clear();\n        this.providers.clear();\n        console.log('🧹 Cleared all registered games');\n    }\n    constructor(){\n        this.games = new Map();\n        this.providers = new Map();\n    }\n}\n// Export singleton instance\nconst gameRegistry = new GameRegistry();\n// Export the class for testing\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./lib/games/registry.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_ENDPOINTS: () => (/* binding */ API_ENDPOINTS),\n/* harmony export */   GAME_CONFIG: () => (/* binding */ GAME_CONFIG),\n/* harmony export */   SessionStorage: () => (/* binding */ SessionStorage),\n/* harmony export */   calculateMultiplier: () => (/* binding */ calculateMultiplier),\n/* harmony export */   calculateProfit: () => (/* binding */ calculateProfit),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   copyToClipboard: () => (/* binding */ copyToClipboard),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   generateClientSeed: () => (/* binding */ generateClientSeed),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   isValidPassword: () => (/* binding */ isValidPassword),\n/* harmony export */   randomInt: () => (/* binding */ randomInt),\n/* harmony export */   shuffleArray: () => (/* binding */ shuffleArray),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   throttle: () => (/* binding */ throttle)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"clsx\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"tailwind-merge\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__]);\n([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n/**\n * Utility function to merge Tailwind CSS classes\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * Format currency values with proper decimal places\n */ function formatCurrency(amount, currency = 'USDT', compact = false) {\n    if (compact && Math.abs(amount) >= 1000) {\n        return formatNumber(amount);\n    }\n    const decimals = currency === 'USDT' ? 2 : 8;\n    return amount.toFixed(decimals);\n}\n/**\n * Format large numbers with K, M, B suffixes\n */ function formatNumber(num) {\n    if (num >= 1e9) {\n        return (num / 1e9).toFixed(1) + 'B';\n    }\n    if (num >= 1e6) {\n        return (num / 1e6).toFixed(1) + 'M';\n    }\n    if (num >= 1e3) {\n        return (num / 1e3).toFixed(1) + 'K';\n    }\n    return num.toString();\n}\n/**\n * Generate a random string for client seeds\n */ function generateClientSeed() {\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n}\n/**\n * Validate email format\n */ function isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n/**\n * Validate password strength\n */ function isValidPassword(password) {\n    // At least 8 characters, 1 uppercase, 1 lowercase, 1 number\n    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$/;\n    return passwordRegex.test(password);\n}\n/**\n * Calculate multiplier based on mines and revealed cells\n */ function calculateMultiplier(mineCount, revealedCount, gridSize = 25) {\n    if (revealedCount === 0) return 1;\n    const safeCells = gridSize - mineCount;\n    const remainingSafeCells = safeCells - revealedCount;\n    if (remainingSafeCells <= 0) return 1;\n    // Base multiplier calculation with house edge\n    const baseMultiplier = safeCells / remainingSafeCells;\n    const houseEdge = 0.04; // 4% house edge\n    return Math.max(1, baseMultiplier * (1 - houseEdge));\n}\n/**\n * Calculate potential profit\n */ function calculateProfit(betAmount, multiplier) {\n    return betAmount * multiplier - betAmount;\n}\n/**\n * Debounce function for performance optimization\n */ function debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\n * Throttle function for performance optimization\n */ function throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n/**\n * Sleep utility for async operations\n */ function sleep(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\n/**\n * Generate a random integer between min and max (inclusive)\n */ function randomInt(min, max) {\n    return Math.floor(Math.random() * (max - min + 1)) + min;\n}\n/**\n * Shuffle array using Fisher-Yates algorithm\n */ function shuffleArray(array) {\n    const shuffled = [\n        ...array\n    ];\n    for(let i = shuffled.length - 1; i > 0; i--){\n        const j = Math.floor(Math.random() * (i + 1));\n        [shuffled[i], shuffled[j]] = [\n            shuffled[j],\n            shuffled[i]\n        ];\n    }\n    return shuffled;\n}\n/**\n * Format date to readable string\n */ function formatDate(date) {\n    const d = new Date(date);\n    return d.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n    });\n}\n/**\n * Copy text to clipboard\n */ async function copyToClipboard(text) {\n    try {\n        await navigator.clipboard.writeText(text);\n        return true;\n    } catch (err) {\n        console.error('Failed to copy text: ', err);\n        return false;\n    }\n}\n/**\n * Game configuration constants\n */ const GAME_CONFIG = {\n    GRID_SIZE: 25,\n    MIN_MINES: 1,\n    MAX_MINES: 24,\n    MIN_BET: 0.01,\n    MAX_BET: 1000,\n    HOUSE_EDGE: 0.04,\n    BASE_MULTIPLIER: 1.0\n};\n/**\n * API endpoints\n */ const API_ENDPOINTS = {\n    AUTH: {\n        LOGIN: '/api/auth/login',\n        SIGNUP: '/api/auth/signup',\n        ME: '/api/auth/me',\n        LOGOUT: '/api/auth/logout'\n    },\n    GAME: {\n        START: '/api/game/start',\n        MOVE: '/api/game/move',\n        CASHOUT: '/api/game/cashout',\n        HISTORY: '/api/game/history',\n        ACTIVE: '/api/game/active',\n        CONFIG: '/api/game/config',\n        LIST: '/api/game/list',\n        STATS: '/api/game/stats'\n    },\n    // Legacy endpoints for backward compatibility\n    MINES: {\n        START: '/api/game/start',\n        PICK: '/api/game/move',\n        CASHOUT: '/api/game/cashout',\n        HISTORY: '/api/game/history'\n    },\n    WALLET: {\n        DEPOSIT: '/api/wallet/deposit',\n        WITHDRAW: '/api/wallet/withdraw',\n        BALANCE: '/api/wallet/balance'\n    }\n};\n/**\n * Session storage utilities for tracking current session stats\n */ const SessionStorage = {\n    SESSION_KEY: 'betoctave_session_stats',\n    /**\n   * Get current session data\n   */ getSession: ()=>{\n        if (true) return null;\n        try {\n            const sessionData = localStorage.getItem(SessionStorage.SESSION_KEY);\n            return sessionData ? JSON.parse(sessionData) : null;\n        } catch (error) {\n            console.error('Error reading session data:', error);\n            return null;\n        }\n    },\n    /**\n   * Initialize or reset session\n   */ resetSession: ()=>{\n        if (true) return;\n        const sessionData = {\n            startTime: new Date().toISOString(),\n            resetCount: (SessionStorage.getSession()?.resetCount || 0) + 1\n        };\n        try {\n            localStorage.setItem(SessionStorage.SESSION_KEY, JSON.stringify(sessionData));\n        } catch (error) {\n            console.error('Error saving session data:', error);\n        }\n    },\n    /**\n   * Get session start time\n   */ getSessionStartTime: ()=>{\n        const session = SessionStorage.getSession();\n        if (session?.startTime) {\n            return session.startTime;\n        }\n        // If no session exists, create one and return its start time\n        SessionStorage.resetSession();\n        return SessionStorage.getSession()?.startTime || new Date().toISOString();\n    },\n    /**\n   * Check if session exists\n   */ hasSession: ()=>{\n        return SessionStorage.getSession() !== null;\n    },\n    /**\n   * Initialize session if it doesn't exist\n   */ initializeSession: ()=>{\n        if (!SessionStorage.hasSession()) {\n            console.log('📊 Initializing new session');\n            SessionStorage.resetSession();\n        } else {\n            console.log('📊 Session already exists:', SessionStorage.getSession());\n        }\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./lib/utils.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"(pages-dir-node)/./node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(pages-dir-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(pages-dir-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"(pages-dir-node)/./pages/_document.tsx\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"(pages-dir-node)/./pages/_app.tsx\");\n/* harmony import */ var _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\index.tsx */ \"(pages-dir-node)/./pages/index.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtcm91dGUtbG9hZGVyL2luZGV4LmpzP2tpbmQ9UEFHRVMmcGFnZT0lMkYmcHJlZmVycmVkUmVnaW9uPSZhYnNvbHV0ZVBhZ2VQYXRoPS4lMkZwYWdlcyU1Q2luZGV4LnRzeCZhYnNvbHV0ZUFwcFBhdGg9cHJpdmF0ZS1uZXh0LXBhZ2VzJTJGX2FwcCZhYnNvbHV0ZURvY3VtZW50UGF0aD1wcml2YXRlLW5leHQtcGFnZXMlMkZfZG9jdW1lbnQmbWlkZGxld2FyZUNvbmZpZ0Jhc2U2ND1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBd0Y7QUFDaEM7QUFDRTtBQUMxRDtBQUN5RDtBQUNWO0FBQy9DO0FBQytDO0FBQy9DO0FBQ0EsaUVBQWUsd0VBQUssQ0FBQyw2Q0FBUSxZQUFZLEVBQUM7QUFDMUM7QUFDTyx1QkFBdUIsd0VBQUssQ0FBQyw2Q0FBUTtBQUNyQyx1QkFBdUIsd0VBQUssQ0FBQyw2Q0FBUTtBQUNyQywyQkFBMkIsd0VBQUssQ0FBQyw2Q0FBUTtBQUN6QyxlQUFlLHdFQUFLLENBQUMsNkNBQVE7QUFDN0Isd0JBQXdCLHdFQUFLLENBQUMsNkNBQVE7QUFDN0M7QUFDTyxnQ0FBZ0Msd0VBQUssQ0FBQyw2Q0FBUTtBQUM5QyxnQ0FBZ0Msd0VBQUssQ0FBQyw2Q0FBUTtBQUM5QyxpQ0FBaUMsd0VBQUssQ0FBQyw2Q0FBUTtBQUMvQyxnQ0FBZ0Msd0VBQUssQ0FBQyw2Q0FBUTtBQUM5QyxvQ0FBb0Msd0VBQUssQ0FBQyw2Q0FBUTtBQUN6RDtBQUNPLHdCQUF3QixrR0FBZ0I7QUFDL0M7QUFDQSxjQUFjLGtFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLGFBQWEsOERBQVc7QUFDeEIsa0JBQWtCLG1FQUFnQjtBQUNsQyxLQUFLO0FBQ0wsWUFBWTtBQUNaLENBQUM7O0FBRUQsaUMiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQYWdlc1JvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9wYWdlcy9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IGhvaXN0IH0gZnJvbSBcIm5leHQvZGlzdC9idWlsZC90ZW1wbGF0ZXMvaGVscGVyc1wiO1xuLy8gSW1wb3J0IHRoZSBhcHAgYW5kIGRvY3VtZW50IG1vZHVsZXMuXG5pbXBvcnQgKiBhcyBkb2N1bWVudCBmcm9tIFwicHJpdmF0ZS1uZXh0LXBhZ2VzL19kb2N1bWVudFwiO1xuaW1wb3J0ICogYXMgYXBwIGZyb20gXCJwcml2YXRlLW5leHQtcGFnZXMvX2FwcFwiO1xuLy8gSW1wb3J0IHRoZSB1c2VybGFuZCBjb2RlLlxuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIi4vcGFnZXNcXFxcaW5kZXgudHN4XCI7XG4vLyBSZS1leHBvcnQgdGhlIGNvbXBvbmVudCAoc2hvdWxkIGJlIHRoZSBkZWZhdWx0IGV4cG9ydCkuXG5leHBvcnQgZGVmYXVsdCBob2lzdCh1c2VybGFuZCwgJ2RlZmF1bHQnKTtcbi8vIFJlLWV4cG9ydCBtZXRob2RzLlxuZXhwb3J0IGNvbnN0IGdldFN0YXRpY1Byb3BzID0gaG9pc3QodXNlcmxhbmQsICdnZXRTdGF0aWNQcm9wcycpO1xuZXhwb3J0IGNvbnN0IGdldFN0YXRpY1BhdGhzID0gaG9pc3QodXNlcmxhbmQsICdnZXRTdGF0aWNQYXRocycpO1xuZXhwb3J0IGNvbnN0IGdldFNlcnZlclNpZGVQcm9wcyA9IGhvaXN0KHVzZXJsYW5kLCAnZ2V0U2VydmVyU2lkZVByb3BzJyk7XG5leHBvcnQgY29uc3QgY29uZmlnID0gaG9pc3QodXNlcmxhbmQsICdjb25maWcnKTtcbmV4cG9ydCBjb25zdCByZXBvcnRXZWJWaXRhbHMgPSBob2lzdCh1c2VybGFuZCwgJ3JlcG9ydFdlYlZpdGFscycpO1xuLy8gUmUtZXhwb3J0IGxlZ2FjeSBtZXRob2RzLlxuZXhwb3J0IGNvbnN0IHVuc3RhYmxlX2dldFN0YXRpY1Byb3BzID0gaG9pc3QodXNlcmxhbmQsICd1bnN0YWJsZV9nZXRTdGF0aWNQcm9wcycpO1xuZXhwb3J0IGNvbnN0IHVuc3RhYmxlX2dldFN0YXRpY1BhdGhzID0gaG9pc3QodXNlcmxhbmQsICd1bnN0YWJsZV9nZXRTdGF0aWNQYXRocycpO1xuZXhwb3J0IGNvbnN0IHVuc3RhYmxlX2dldFN0YXRpY1BhcmFtcyA9IGhvaXN0KHVzZXJsYW5kLCAndW5zdGFibGVfZ2V0U3RhdGljUGFyYW1zJyk7XG5leHBvcnQgY29uc3QgdW5zdGFibGVfZ2V0U2VydmVyUHJvcHMgPSBob2lzdCh1c2VybGFuZCwgJ3Vuc3RhYmxlX2dldFNlcnZlclByb3BzJyk7XG5leHBvcnQgY29uc3QgdW5zdGFibGVfZ2V0U2VydmVyU2lkZVByb3BzID0gaG9pc3QodXNlcmxhbmQsICd1bnN0YWJsZV9nZXRTZXJ2ZXJTaWRlUHJvcHMnKTtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IFBhZ2VzUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLlBBR0VTLFxuICAgICAgICBwYWdlOiBcIi9pbmRleFwiLFxuICAgICAgICBwYXRobmFtZTogXCIvXCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogJycsXG4gICAgICAgIGZpbGVuYW1lOiAnJ1xuICAgIH0sXG4gICAgY29tcG9uZW50czoge1xuICAgICAgICAvLyBkZWZhdWx0IGV4cG9ydCBtaWdodCBub3QgZXhpc3Qgd2hlbiBvcHRpbWl6ZWQgZm9yIGRhdGEgb25seVxuICAgICAgICBBcHA6IGFwcC5kZWZhdWx0LFxuICAgICAgICBEb2N1bWVudDogZG9jdW1lbnQuZGVmYXVsdFxuICAgIH0sXG4gICAgdXNlcmxhbmRcbn0pO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1wYWdlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"(pages-dir-node)/./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"(pages-dir-node)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(pages-dir-node)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_UniversalGameContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/UniversalGameContext */ \"(pages-dir-node)/./contexts/UniversalGameContext.tsx\");\n/* harmony import */ var _contexts_MinesGameContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/MinesGameContext */ \"(pages-dir-node)/./contexts/MinesGameContext.tsx\");\n/* harmony import */ var _contexts_DiceGameContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/DiceGameContext */ \"(pages-dir-node)/./contexts/DiceGameContext.tsx\");\n/* harmony import */ var _contexts_CrashGameContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/CrashGameContext */ \"(pages-dir-node)/./contexts/CrashGameContext.tsx\");\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/toast */ \"(pages-dir-node)/./components/ui/toast.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__, _contexts_UniversalGameContext__WEBPACK_IMPORTED_MODULE_4__, _contexts_MinesGameContext__WEBPACK_IMPORTED_MODULE_5__, _contexts_DiceGameContext__WEBPACK_IMPORTED_MODULE_6__, _contexts_CrashGameContext__WEBPACK_IMPORTED_MODULE_7__, _components_ui_toast__WEBPACK_IMPORTED_MODULE_8__]);\n([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__, _contexts_UniversalGameContext__WEBPACK_IMPORTED_MODULE_4__, _contexts_MinesGameContext__WEBPACK_IMPORTED_MODULE_5__, _contexts_DiceGameContext__WEBPACK_IMPORTED_MODULE_6__, _contexts_CrashGameContext__WEBPACK_IMPORTED_MODULE_7__, _components_ui_toast__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"BetOctave - Provably Fair Crypto Gambling\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_app.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_app.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Experience the thrill of provably fair crypto gambling with multiple games, transparent mechanics, and instant payouts.\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_app.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_app.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_app.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_8__.ToastProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_UniversalGameContext__WEBPACK_IMPORTED_MODULE_4__.UniversalGameProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_MinesGameContext__WEBPACK_IMPORTED_MODULE_5__.MinesGameProvider, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_DiceGameContext__WEBPACK_IMPORTED_MODULE_6__.DiceGameProvider, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_CrashGameContext__WEBPACK_IMPORTED_MODULE_7__.CrashGameProvider, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                                        ...pageProps\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_app.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_app.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_app.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_app.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_app.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_app.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_app.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_app.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_document.tsx":
/*!*****************************!*\
  !*** ./pages/_document.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"(pages-dir-node)/./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"en\",\n        className: \"dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"BetOctave - Provably Fair Crypto Gambling Platform\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_document.tsx\",\n                        lineNumber: 7,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_document.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_document.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"bg-background text-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_document.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_document.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_document.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\_document.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL19kb2N1bWVudC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTZEO0FBRTlDLFNBQVNJO0lBQ3RCLHFCQUNFLDhEQUFDSiwrQ0FBSUE7UUFBQ0ssTUFBSztRQUFLQyxXQUFVOzswQkFDeEIsOERBQUNMLCtDQUFJQTs7a0NBQ0gsOERBQUNNO3dCQUFLQyxNQUFLO3dCQUFjQyxTQUFROzs7Ozs7a0NBQ2pDLDhEQUFDQzt3QkFBS0MsS0FBSTt3QkFBT0MsTUFBSzs7Ozs7Ozs7Ozs7OzBCQUV4Qiw4REFBQ0M7Z0JBQUtQLFdBQVU7O2tDQUNkLDhEQUFDSiwrQ0FBSUE7Ozs7O2tDQUNMLDhEQUFDQyxxREFBVUE7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSW5CIiwic291cmNlcyI6WyJFOlxcMTExXFxQUk9KRUNUXFxtaW5lcy1nYW1lXFxwYWdlc1xcX2RvY3VtZW50LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBIdG1sLCBIZWFkLCBNYWluLCBOZXh0U2NyaXB0IH0gZnJvbSAnbmV4dC9kb2N1bWVudCc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIERvY3VtZW50KCkge1xuICByZXR1cm4gKFxuICAgIDxIdG1sIGxhbmc9XCJlblwiIGNsYXNzTmFtZT1cImRhcmtcIj5cbiAgICAgIDxIZWFkPlxuICAgICAgICA8bWV0YSBuYW1lPVwiZGVzY3JpcHRpb25cIiBjb250ZW50PVwiQmV0T2N0YXZlIC0gUHJvdmFibHkgRmFpciBDcnlwdG8gR2FtYmxpbmcgUGxhdGZvcm1cIiAvPlxuICAgICAgICA8bGluayByZWw9XCJpY29uXCIgaHJlZj1cIi9mYXZpY29uLmljb1wiIC8+XG4gICAgICA8L0hlYWQ+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9XCJiZy1iYWNrZ3JvdW5kIHRleHQtZm9yZWdyb3VuZFwiPlxuICAgICAgICA8TWFpbiAvPlxuICAgICAgICA8TmV4dFNjcmlwdCAvPlxuICAgICAgPC9ib2R5PlxuICAgIDwvSHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJIdG1sIiwiSGVhZCIsIk1haW4iLCJOZXh0U2NyaXB0IiwiRG9jdW1lbnQiLCJsYW5nIiwiY2xhc3NOYW1lIiwibWV0YSIsIm5hbWUiLCJjb250ZW50IiwibGluayIsInJlbCIsImhyZWYiLCJib2R5Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_document.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-node)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(pages-dir-node)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(pages-dir-node)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(pages-dir-node)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Gem_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Gem,Shield,TrendingUp,Zap!=!lucide-react */ \"(pages-dir-node)/__barrel_optimize__?names=Gem,Shield,TrendingUp,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__, _components_ui_button__WEBPACK_IMPORTED_MODULE_4__, _components_ui_card__WEBPACK_IMPORTED_MODULE_5__]);\n([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__, _components_ui_button__WEBPACK_IMPORTED_MODULE_4__, _components_ui_card__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nfunction Home() {\n    const { user, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (!loading && user) {\n                router.push('/lobby');\n            }\n        }\n    }[\"Home.useEffect\"], [\n        user,\n        loading,\n        router\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-white\"\n            }, void 0, false, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this);\n    }\n    if (user) {\n        return null; // Will redirect to /lobby\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"container mx-auto px-4 py-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Gem_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Gem, {\n                                    className: \"h-8 w-8 text-purple-400\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: \"BetOctave\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>router.push('/login'),\n                                    className: \"border-purple-400 text-purple-400 hover:bg-purple-400 hover:text-white\",\n                                    children: \"Login\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>router.push('/signup'),\n                                    className: \"bg-purple-600 hover:bg-purple-700 text-white\",\n                                    children: \"Sign Up\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 py-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-6xl font-bold text-white mb-6\",\n                                children: [\n                                    \"Provably Fair\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400\",\n                                        children: [\n                                            \" \",\n                                            \"Gambling\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-300 mb-8 max-w-2xl mx-auto\",\n                                children: \"Experience the thrill of crypto gambling with our provably fair gaming platform. Play multiple games, choose your risk, and win big with transparent mechanics!\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        size: \"lg\",\n                                        onClick: ()=>router.push('/signup'),\n                                        className: \"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 py-3 text-lg\",\n                                        children: \"Start Playing\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        size: \"lg\",\n                                        variant: \"outline\",\n                                        onClick: ()=>router.push('/login'),\n                                        className: \"border-purple-400 text-purple-400 hover:bg-purple-400 hover:text-white px-8 py-3 text-lg\",\n                                        children: \"Login\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                className: \"bg-gray-800/50 border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Gem_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Shield, {\n                                                className: \"h-8 w-8 text-green-400 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                className: \"text-white\",\n                                                children: \"Provably Fair\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            className: \"text-gray-300\",\n                                            children: \"Verify every game result with our transparent cryptographic system\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                className: \"bg-gray-800/50 border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Gem_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Zap, {\n                                                className: \"h-8 w-8 text-yellow-400 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                className: \"text-white\",\n                                                children: \"Instant Payouts\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            className: \"text-gray-300\",\n                                            children: \"Cash out instantly with USDT and LTC support\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                className: \"bg-gray-800/50 border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Gem_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__.TrendingUp, {\n                                                className: \"h-8 w-8 text-blue-400 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                className: \"text-white\",\n                                                children: \"Multiple Games\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            className: \"text-gray-300\",\n                                            children: \"Enjoy various gambling games with different risk levels and rewards\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                className: \"bg-gray-800/50 border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Gem_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Gem, {\n                                                className: \"h-8 w-8 text-purple-400 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                className: \"text-white\",\n                                                children: \"Mobile Ready\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            className: \"text-gray-300\",\n                                            children: \"Play anywhere with our responsive mobile-first design\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl font-bold text-white mb-8\",\n                                children: \"How to Play\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-3 gap-8 max-w-4xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-purple-600 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl font-bold text-white\",\n                                                    children: \"1\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold text-white mb-2\",\n                                                children: \"Choose Your Game\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-300\",\n                                                children: \"Select from our variety of games and set your bet amount\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-purple-600 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl font-bold text-white\",\n                                                    children: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold text-white mb-2\",\n                                                children: \"Play & Win\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-300\",\n                                                children: \"Make your moves and watch your multiplier grow with each success\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-purple-600 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl font-bold text-white\",\n                                                    children: \"3\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold text-white mb-2\",\n                                                children: \"Cash Out\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-300\",\n                                                children: \"Cash out anytime to secure your winnings before it's too late\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"container mx-auto px-4 py-8 border-t border-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center text-gray-400\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"\\xa9 2024 BetOctave. All rights reserved. Play responsibly.\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\111\\\\PROJECT\\\\mines-game\\\\pages\\\\index.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/index.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=Gem,Shield,TrendingUp,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!******************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Gem,Shield,TrendingUp,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Gem: () => (/* reexport safe */ _icons_gem_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Shield: () => (/* reexport safe */ _icons_shield_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   TrendingUp: () => (/* reexport safe */ _icons_trending_up_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Zap: () => (/* reexport safe */ _icons_zap_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_gem_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/gem.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/gem.js\");\n/* harmony import */ var _icons_shield_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/shield.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _icons_trending_up_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/trending-up.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _icons_zap_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/zap.js */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUdlbSxTaGllbGQsVHJlbmRpbmdVcCxaYXAhPSEuL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUMrQztBQUNNO0FBQ1MiLCJzb3VyY2VzIjpbIkU6XFwxMTFcXFBST0pFQ1RcXG1pbmVzLWdhbWVcXG5vZGVfbW9kdWxlc1xcbHVjaWRlLXJlYWN0XFxkaXN0XFxlc21cXGx1Y2lkZS1yZWFjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgR2VtIH0gZnJvbSBcIi4vaWNvbnMvZ2VtLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU2hpZWxkIH0gZnJvbSBcIi4vaWNvbnMvc2hpZWxkLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgVHJlbmRpbmdVcCB9IGZyb20gXCIuL2ljb25zL3RyZW5kaW5nLXVwLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgWmFwIH0gZnJvbSBcIi4vaWNvbnMvemFwLmpzXCIiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/__barrel_optimize__?names=Gem,Shield,TrendingUp,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=X!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!******************************************************************************************!*\
  !*** __barrel_optimize__?names=X!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   X: () => (/* reexport safe */ _icons_x_js__WEBPACK_IMPORTED_MODULE_0__["default"])
/* harmony export */ });
/* harmony import */ var _icons_x_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/x.js */ "(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/x.js");



/***/ }),

/***/ "@radix-ui/react-slot":
/*!***************************************!*\
  !*** external "@radix-ui/react-slot" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@radix-ui/react-slot");;

/***/ }),

/***/ "@radix-ui/react-toast":
/*!****************************************!*\
  !*** external "@radix-ui/react-toast" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@radix-ui/react-toast");;

/***/ }),

/***/ "bcryptjs?a1e7":
/*!***************************!*\
  !*** external "bcryptjs" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("bcryptjs");

/***/ }),

/***/ "class-variance-authority":
/*!*******************************************!*\
  !*** external "class-variance-authority" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = import("class-variance-authority");;

/***/ }),

/***/ "clsx":
/*!***********************!*\
  !*** external "clsx" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = import("clsx");;

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tailwind-merge":
/*!*********************************!*\
  !*** external "tailwind-merge" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("tailwind-merge");;

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();