/**
 * Automated Crash Game Testing Script
 * This script tests the crash game functionality by simulating user interactions
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';
let authCookie = '';

// Test user credentials
const testUser = {
  username: 'crashtest',
  email: '<EMAIL>',
  password: 'TestPass123'
};

/**
 * Helper function to make authenticated requests
 */
async function makeRequest(endpoint, options = {}) {
  const url = `${BASE_URL}${endpoint}`;
  const headers = {
    'Content-Type': 'application/json',
    ...options.headers
  };

  if (authCookie) {
    headers['Cookie'] = authCookie;
  }

  const response = await fetch(url, {
    ...options,
    headers,
    credentials: 'include'
  });

  // Extract cookies from response
  const setCookieHeader = response.headers.get('set-cookie');
  if (setCookieHeader) {
    authCookie = setCookieHeader;
  }

  return response;
}

/**
 * Test 1: Create test account
 */
async function testCreateAccount() {
  console.log('🧪 Test 1: Creating test account...');
  
  try {
    const response = await makeRequest('/api/auth/signup', {
      method: 'POST',
      body: JSON.stringify(testUser)
    });

    const data = await response.json();
    
    if (data.success) {
      console.log('✅ Test account created successfully');
      console.log(`   User ID: ${data.user.id}`);
      console.log(`   Balance: $${data.user.usdt_balance}`);
      return true;
    } else {
      console.log('❌ Failed to create test account:', data.error);
      return false;
    }
  } catch (error) {
    console.log('❌ Error creating test account:', error.message);
    return false;
  }
}

/**
 * Test 2: Add test balance
 */
async function testAddBalance() {
  console.log('🧪 Test 2: Adding test balance...');
  
  try {
    const response = await makeRequest('/api/test/add-balance', {
      method: 'POST',
      body: JSON.stringify({ amount: 100 })
    });

    const data = await response.json();
    
    if (data.success) {
      console.log('✅ Test balance added successfully');
      console.log(`   New balance: $${data.balance.usdt}`);
      return true;
    } else {
      console.log('❌ Failed to add test balance:', data.error);
      return false;
    }
  } catch (error) {
    console.log('❌ Error adding test balance:', error.message);
    return false;
  }
}

/**
 * Test 3: Start crash game
 */
async function testStartCrashGame(betAmount = 1) {
  console.log(`🧪 Test 3: Starting crash game with $${betAmount} bet...`);
  
  try {
    const response = await makeRequest('/api/game/start', {
      method: 'POST',
      body: JSON.stringify({
        game_type: 'crash',
        bet_amount: betAmount
      })
    });

    const data = await response.json();
    
    if (data.success) {
      console.log('✅ Crash game started successfully');
      console.log(`   Game ID: ${data.game?.id || 'N/A'}`);
      console.log(`   Bet Amount: $${data.game?.bet_amount || 'N/A'}`);
      console.log(`   Status: ${data.game?.status || 'N/A'}`);
      return data.game || data.gameState;
    } else {
      console.log('❌ Failed to start crash game:', data.error);
      return null;
    }
  } catch (error) {
    console.log('❌ Error starting crash game:', error.message);
    return null;
  }
}

/**
 * Test 4: Cash out crash game
 */
async function testCashOut(gameState) {
  console.log('🧪 Test 4: Attempting to cash out...');

  if (!gameState) {
    console.log('❌ No game state provided for cash out');
    return false;
  }

  try {
    const response = await makeRequest('/api/game/cashout', {
      method: 'POST',
      body: JSON.stringify({
        game_id: gameState.id || 'active',
        game_type: 'crash'
      })
    });

    const data = await response.json();
    
    if (data.success) {
      console.log('✅ Cash out successful');
      console.log(`   Profit: $${data.profit}`);
      console.log(`   Final multiplier: ${data.gameState?.current_multiplier || 'N/A'}`);
      return true;
    } else {
      console.log('❌ Cash out failed:', data.error);
      return false;
    }
  } catch (error) {
    console.log('❌ Error during cash out:', error.message);
    return false;
  }
}

/**
 * Test 5: Check game history
 */
async function testGameHistory() {
  console.log('🧪 Test 5: Checking game history...');
  
  try {
    const response = await makeRequest('/api/game/history?game_type=crash');
    const data = await response.json();
    
    if (data.success) {
      console.log('✅ Game history retrieved successfully');
      console.log(`   Total games: ${data.games.length}`);
      if (data.games.length > 0) {
        const lastGame = data.games[0];
        console.log(`   Last game: ${lastGame.status} - $${lastGame.profit || 0} profit`);
      }
      return true;
    } else {
      console.log('❌ Failed to get game history:', data.error);
      return false;
    }
  } catch (error) {
    console.log('❌ Error getting game history:', error.message);
    return false;
  }
}

/**
 * Test 6: Test auto cash out
 */
async function testAutoCashOut() {
  console.log('🧪 Test 6: Testing auto cash out at 2.00x...');
  
  try {
    const response = await makeRequest('/api/game/start', {
      method: 'POST',
      body: JSON.stringify({
        game_type: 'crash',
        bet_amount: 5,
        auto_cash_out: 2.0
      })
    });

    const data = await response.json();
    
    if (data.success) {
      console.log('✅ Auto cash out game started successfully');
      console.log(`   Game ID: ${data.gameState.id}`);
      console.log(`   Auto cash out at: ${data.gameState.auto_cash_out}x`);
      return data.gameState;
    } else {
      console.log('❌ Failed to start auto cash out game:', data.error);
      return null;
    }
  } catch (error) {
    console.log('❌ Error starting auto cash out game:', error.message);
    return null;
  }
}

/**
 * Main test runner
 */
async function runTests() {
  console.log('🚀 Starting Crash Game Testing Suite\n');
  
  let testsPassed = 0;
  let totalTests = 6;

  // Test 1: Create account
  if (await testCreateAccount()) testsPassed++;
  console.log('');

  // Test 2: Add balance
  if (await testAddBalance()) testsPassed++;
  console.log('');

  // Test 3: Start crash game
  const gameState = await testStartCrashGame(10);
  if (gameState) testsPassed++;
  console.log('');

  // Wait a moment to simulate game play
  if (gameState) {
    console.log('⏳ Waiting 2 seconds to simulate game play...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Test 4: Cash out
    if (await testCashOut(gameState)) testsPassed++;
    console.log('');
  }

  // Test 5: Check history
  if (await testGameHistory()) testsPassed++;
  console.log('');

  // Test 6: Auto cash out
  const autoCashOutGame = await testAutoCashOut();
  if (autoCashOutGame) testsPassed++;
  console.log('');

  // Summary
  console.log('📊 Test Results Summary:');
  console.log(`   Tests Passed: ${testsPassed}/${totalTests}`);
  console.log(`   Success Rate: ${((testsPassed/totalTests) * 100).toFixed(1)}%`);
  
  if (testsPassed === totalTests) {
    console.log('🎉 All tests passed! Crash game is working correctly.');
  } else {
    console.log('⚠️  Some tests failed. Check the logs above for details.');
  }
}

// Run the tests
runTests().catch(console.error);
