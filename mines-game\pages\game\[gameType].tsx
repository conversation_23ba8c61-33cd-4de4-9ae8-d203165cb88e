import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useAuth } from '@/contexts/AuthContext';
import { useMinesGame } from '@/contexts/MinesGameContext';
import { useDiceGame } from '@/contexts/DiceGameContext';
import { useCrashGame } from '@/contexts/CrashGameContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { GameGrid } from '@/components/game/GameGrid';
import { GameControls } from '@/components/game/GameControls';
import { DiceControls } from '@/components/game/dice/DiceControls';
import { DiceDisplay } from '@/components/game/dice/DiceDisplay';
import { CrashControls } from '@/components/game/crash/CrashControls';
import { CrashDisplay } from '@/components/game/crash/CrashDisplay';
import { WalletModal } from '@/components/wallet/WalletModal';
import { GameHistory } from '@/components/game/GameHistory';
import { LiveStats } from '@/components/game/LiveStats';
import {
  Gem,
  LogOut,
  History,
  Wallet,
  Volume2,
  VolumeX,
  BarChart3,
  ArrowLeft,
  Home
} from 'lucide-react';
import { formatCurrency, SessionStorage } from '@/lib/utils';
import { soundManager } from '@/lib/sounds';
import { GameMessage, GameMessageType } from '@/components/game/GameMessage';
import { Toaster } from '@/components/ui/toaster';
import { useToast } from '@/components/ui/use-toast';
import { GameType } from '@/types';

export default function GamePage() {
  const { user, logout, loading: authLoading } = useAuth();
  const minesGameContext = useMinesGame();
  const diceGameContext = useDiceGame();
  const crashGameContext = useCrashGame();
  const router = useRouter();
  const { toast } = useToast();
  const { gameType } = router.query;

  // Get the appropriate game context based on game type
  const currentGameContext = gameType === 'dice' ? diceGameContext :
                            gameType === 'crash' ? crashGameContext :
                            minesGameContext;
  const {
    gameState,
    loading: gameLoading
  } = currentGameContext;

  // UI state
  const [showHistory, setShowHistory] = useState(false);
  const [showWallet, setShowWallet] = useState(false);
  const [showStats, setShowStats] = useState(false);
  const [statsRefreshTrigger, setStatsRefreshTrigger] = useState(0);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [gameConfig, setGameConfig] = useState<any>(null);

  // Game message state
  const [gameMessage, setGameMessage] = useState<{
    type: GameMessageType;
    visible: boolean;
    multiplier?: number;
    profit?: number;
  } | null>(null);

  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/login');
    }
  }, [user, authLoading, router]);

  useEffect(() => {
    soundManager.setEnabled(soundEnabled);
  }, [soundEnabled]);

  // Initialize session storage when component mounts
  useEffect(() => {
    SessionStorage.initializeSession();
  }, []);

  // Load game configuration
  useEffect(() => {
    if (gameType && typeof gameType === 'string') {
      loadGameConfig(gameType as GameType);
    }
  }, [gameType]);

  const loadGameConfig = async (type: GameType) => {
    try {
      const response = await fetch(`/api/game/list?active=true`);
      const data = await response.json();

      if (data.success) {
        const config = data.games.find((game: any) => game.id === type);
        if (config) {
          setGameConfig(config);
        } else {
          // Game not found, redirect to lobby
          toast({
            title: "Game Not Found",
            description: "The requested game is not available.",
            variant: "destructive",
          });
          router.push('/lobby');
        }
      }
    } catch (error) {
      console.error('Failed to load game config:', error);
      router.push('/lobby');
    }
  };

  // Function to show game messages
  const showGameMessage = (type: GameMessageType, multiplier?: number, profit?: number) => {
    setGameMessage({
      type,
      visible: true,
      multiplier,
      profit
    });
  };

  const hideGameMessage = () => {
    setGameMessage(null);
  };

  const triggerStatsRefresh = () => {
    setStatsRefreshTrigger(prev => prev + 1);
  };

  const handleStartGame = async (betAmount: number, mineCount: number) => {
    try {
      const success = await minesGameContext.startGame(betAmount, mineCount);
      if (success) {
        soundManager.play('click');
      }
    } catch (error) {
      console.error('Failed to start game:', error);
    }
  };

  // Dice game handlers
  const handleStartDiceGame = async (betAmount: number, targetNumber: number, rollUnder: boolean) => {
    try {
      const success = await diceGameContext.startGame(betAmount, targetNumber, rollUnder);
      if (success) {
        soundManager.play('click');
      }
    } catch (error) {
      console.error('Failed to start dice game:', error);
    }
  };

  const handleRollDice = async () => {
    try {
      const result = await diceGameContext.rollDice();

      if (result.won) {
        soundManager.play('win');
        setTimeout(() => {
          showGameMessage('win', result.multiplier, result.profit);
          triggerStatsRefresh();
        }, 500);
      } else {
        soundManager.play('lose');
        setTimeout(() => {
          showGameMessage('lost', result.multiplier, result.profit);
          triggerStatsRefresh();
        }, 500);
      }
    } catch (error) {
      console.error('Failed to roll dice:', error);
    }
  };

  // Crash game handlers
  const handleStartCrashGame = async (betAmount: number, autoCashOut?: number) => {
    try {
      const success = await crashGameContext.startGame(betAmount, autoCashOut);
      if (success) {
        soundManager.play('click');
      }
    } catch (error) {
      console.error('Failed to start crash game:', error);
    }
  };

  const handleCrashCashOut = async () => {
    try {
      const result = await crashGameContext.cashOut();
      if (result.success) {
        soundManager.play('cashout');
        setTimeout(() => {
          showGameMessage('cashout', result.multiplier, result.profit);
          triggerStatsRefresh();
        }, 200);

        toast({
          title: "Successfully Cashed Out!",
          description: `Profit: +${result.profit.toFixed(2)} USDT at ${result.multiplier.toFixed(2)}x`,
          variant: "success",
          duration: 3000,
        });
      }
    } catch (error) {
      console.error('Failed to cash out:', error);
    }
  };

  const handleCellClick = async (cellIndex: number) => {
    if (!gameState || gameState.status !== 'active' || gameLoading) return;
    const revealedCells = (gameState as any)?.revealed_cells || [];
    if (revealedCells.includes(cellIndex)) return;

    try {
      const result = await minesGameContext.revealCell(cellIndex);

      if (result.hit) {
        // Mine hit - show boom message
        soundManager.play('mine');
        setTimeout(() => {
          soundManager.play('lose');
          showGameMessage('boom');
          // Refresh stats after game ends
          triggerStatsRefresh();
        }, 300);
      } else {
        soundManager.play('reveal');

        if (result.gameOver) {
          // Perfect win - found all safe cells
          setTimeout(() => {
            soundManager.play('win');
            showGameMessage('perfect_win', result.multiplier, result.profit || gameState.profit);
            // Refresh stats after game ends
            triggerStatsRefresh();
          }, 500);
        } else {
          // Safe cell revealed - only show subtle toast for multiplier updates, no full-screen message
          if (result.multiplier > 1.5) {
            toast({
              title: `${result.multiplier.toFixed(2)}x Multiplier!`,
              description: `Potential profit: ${gameState.profit?.toFixed(2)} USDT`,
              variant: "info",
              duration: 2000,
            });
          }
        }
      }
    } catch (error) {
      console.error('Failed to reveal cell:', error);
    }
  };

  const handleCashOut = async () => {
    if (!gameState || gameState.status !== 'active') return;

    try {
      const result = await minesGameContext.cashOut();
      if (result.success) {
        soundManager.play('cashout');

        // Show big win message for high multipliers, otherwise show regular cashout
        const isBigWin = gameState.current_multiplier >= 5.0;
        setTimeout(() => {
          if (isBigWin) {
            showGameMessage('big_win', gameState.current_multiplier, result.profit);
          } else {
            showGameMessage('cashout', gameState.current_multiplier, result.profit);
          }
          // Refresh stats after cashout
          triggerStatsRefresh();
        }, 200);

        // Also show a success toast
        toast({
          title: "Successfully Cashed Out!",
          description: `Profit: +${result.profit.toFixed(2)} USDT`,
          variant: "success",
          duration: 3000,
        });
      }
    } catch (error) {
      console.error('Failed to cash out:', error);
    }
  };

  const refreshUserData = async () => {
    // This would typically refresh user data from the auth context
    // For now, we'll just close the wallet modal
    setShowWallet(false);
  };

  const handleBackToLobby = () => {
    soundManager.play('click');
    router.push('/lobby');
  };

  if (authLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-white"></div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect to login
  }

  // Show loading while game config is being loaded
  if (!gameConfig) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-white"></div>
      </div>
    );
  }

  // Handle unsupported games
  if (gameType !== 'mines' && gameType !== 'dice' && gameType !== 'crash') {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <Card className="bg-gray-800/80 border-gray-600 max-w-md mx-auto">
          <CardContent className="p-8 text-center">
            <div className="text-6xl mb-4">{gameConfig.icon}</div>
            <h2 className="text-2xl font-bold text-white mb-4">{gameConfig.name}</h2>
            <p className="text-gray-400 mb-6">This game is coming soon! Stay tuned for updates.</p>
            <Button onClick={handleBackToLobby} className="bg-purple-600 hover:bg-purple-700">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Lobby
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Render Mines game
  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <header className="bg-gray-800/50 border-b border-gray-700">
        <div className="container mx-auto px-4 py-3">
          <nav className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBackToLobby}
                className="text-gray-400 hover:text-white hover:bg-gray-700"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Lobby
              </Button>

              <div className="flex items-center space-x-2">
                <Gem className="h-6 w-6 text-purple-400" />
                <span className="text-xl font-bold text-white">BetOctave</span>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <div className="bg-gray-700/50 px-3 py-1 rounded-md text-white text-sm">
                <span className="text-gray-300">{formatCurrency(user.usdt_balance)} </span>
                <span className="text-orange-400">₿</span>
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowWallet(true)}
                className="bg-blue-600 hover:bg-blue-700 text-white border-blue-600"
              >
                <Wallet className="h-4 w-4 sm:mr-2" />
                <span className="hidden sm:inline">Wallet</span>
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setSoundEnabled(!soundEnabled)}
                className="border-gray-500 text-gray-400 hover:bg-gray-600 hover:text-white"
              >
                {soundEnabled ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowHistory(true)}
                className="border-gray-500 text-gray-400 hover:bg-gray-600 hover:text-white"
              >
                <History className="h-4 w-4" />
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowStats(true)}
                className="border-gray-500 text-gray-400 hover:bg-gray-600 hover:text-white"
              >
                <BarChart3 className="h-4 w-4" />
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={logout}
                className="border-gray-500 text-gray-400 hover:bg-gray-600 hover:text-white"
              >
                <LogOut className="h-4 w-4" />
              </Button>
            </div>
          </nav>
        </div>
      </header>

      {/* Main Game Area */}
      <main className="container mx-auto px-4 py-6">
        {gameType === 'crash' ? (
          // Crash Game Layout
          <div className="grid lg:grid-cols-4 gap-6">
            {/* Crash Controls */}
            <div className="lg:col-span-1 order-2 lg:order-1">
              <CrashControls
                user={user}
                gameState={gameState as any}
                onStartGame={handleStartCrashGame}
                onCashOut={handleCrashCashOut}
                loading={gameLoading}
                canPlaceBet={crashGameContext.canPlaceBet()}
                canCashOut={crashGameContext.canCashOut()}
                currentMultiplier={crashGameContext.getCurrentMultiplier()}
                roundPhase={crashGameContext.getRoundPhase()}
                timeUntilNextRound={crashGameContext.getTimeUntilNextRound()}
                getCrashStats={crashGameContext.getCrashStats}
              />
            </div>

            {/* Crash Display */}
            <div className="lg:col-span-3 order-1 lg:order-2">
              <CrashDisplay
                gameState={gameState as any}
                currentMultiplier={crashGameContext.getCurrentMultiplier()}
                roundPhase={crashGameContext.getRoundPhase()}
                timeElapsed={crashGameContext.getTimeElapsed()}
                timeUntilNextRound={crashGameContext.getTimeUntilNextRound()}
              />
            </div>
          </div>
        ) : gameType === 'dice' ? (
          // Dice Game Layout
          <div className="grid lg:grid-cols-4 gap-6">
            {/* Dice Controls */}
            <div className="lg:col-span-1 order-2 lg:order-1">
              <DiceControls
                user={user}
                gameState={gameState as any}
                onStartGame={handleStartDiceGame}
                onRollDice={handleRollDice}
                loading={gameLoading}
                canRollDice={diceGameContext.canRollDice()}
                calculateWinChance={diceGameContext.calculateWinChance}
                calculateMultiplier={diceGameContext.calculateMultiplier}
              />
            </div>

            {/* Dice Display */}
            <div className="lg:col-span-3 order-1 lg:order-2">
              <div className="bg-gray-800/80 rounded-xl border border-gray-600 p-6 backdrop-blur-sm shadow-2xl">
                {/* Game Header */}
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                      <span className="text-white text-lg">{gameConfig.icon}</span>
                    </div>
                    <div>
                      <h2 className="text-xl font-bold text-white">{gameConfig.name}</h2>
                      <p className="text-sm text-gray-400">BetOctave Originals</p>
                    </div>
                  </div>

                  {gameState && (
                    <div className="text-right">
                      <div className="text-sm text-gray-400">Game ID</div>
                      <div className="text-sm text-gray-300 font-mono">#{gameState.id?.toString().slice(-8) || 'N/A'}</div>
                    </div>
                  )}
                </div>

                {/* Dice Game Container */}
                <DiceDisplay
                  gameState={gameState as any}
                  loading={gameLoading}
                />

                {/* Game Footer */}
                <div className="mt-4 flex items-center justify-between">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowStats(true)}
                    className="text-gray-400 hover:text-white hover:bg-gray-700/50 p-2 rounded-lg"
                  >
                    <BarChart3 className="h-4 w-4 mr-1" />
                    <span className="text-xs">Stats</span>
                  </Button>
                  <p className="text-xs text-gray-500">Provably Fair</p>
                  <div className="w-16"></div> {/* Spacer for balance */}
                </div>
              </div>
            </div>
          </div>
        ) : (
          // Mines Game Layout
          <div className="grid lg:grid-cols-4 gap-6">
            {/* Game Controls */}
            <div className="lg:col-span-1 order-2 lg:order-1">
              <GameControls
                user={user}
                gameState={gameState}
                onStartGame={handleStartGame}
                onCashOut={handleCashOut}
                loading={gameLoading}
              />
            </div>

            {/* Game Grid */}
            <div className="lg:col-span-3 order-1 lg:order-2">
              <div className="bg-gray-800/80 rounded-xl border border-gray-600 p-6 backdrop-blur-sm shadow-2xl">
                {/* Game Header */}
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                      <span className="text-white text-lg">{gameConfig.icon}</span>
                    </div>
                    <div>
                      <h2 className="text-xl font-bold text-white">{gameConfig.name}</h2>
                      <p className="text-sm text-gray-400">BetOctave Originals</p>
                    </div>
                  </div>

                  {gameState && (
                    <div className="text-right">
                      <div className="text-sm text-gray-400">Game ID</div>
                      <div className="text-sm text-gray-300 font-mono">#{gameState.id?.toString().slice(-8) || 'N/A'}</div>
                    </div>
                  )}
                </div>

                {/* Game Grid Container */}
                <div className="bg-gray-900/50 rounded-lg p-6 border border-gray-700">
                  <GameGrid
                    gameState={gameState}
                    onCellClick={handleCellClick}
                    loading={gameLoading}
                  />
                </div>

                {/* Game Footer */}
                <div className="mt-4 flex items-center justify-between">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowStats(true)}
                    className="text-gray-400 hover:text-white hover:bg-gray-700/50 p-2 rounded-lg"
                  >
                    <BarChart3 className="h-4 w-4 mr-1" />
                    <span className="text-xs">Stats</span>
                  </Button>
                  <p className="text-xs text-gray-500">Fairness</p>
                  <div className="w-16"></div> {/* Spacer for balance */}
                </div>
              </div>
            </div>
          </div>
        )}
      </main>

      {/* Modals */}
      <WalletModal
        user={user}
        isOpen={showWallet}
        onClose={() => setShowWallet(false)}
        onBalanceUpdate={refreshUserData}
      />

      <GameHistory
        isOpen={showHistory}
        onClose={() => setShowHistory(false)}
      />

      <LiveStats
        isOpen={showStats}
        onClose={() => setShowStats(false)}
        refreshTrigger={statsRefreshTrigger}
      />

      {/* Game Messages */}
      {gameMessage && (
        <GameMessage
          type={gameMessage.type}
          visible={gameMessage.visible}
          multiplier={gameMessage.multiplier}
          profit={gameMessage.profit}
          onComplete={hideGameMessage}
        />
      )}

      {/* Toast Notifications */}
      <Toaster />
    </div>
  );
}