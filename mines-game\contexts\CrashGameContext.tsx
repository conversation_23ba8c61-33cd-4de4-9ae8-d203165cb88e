import React, { createContext, useContext, useEffect, useState, useRef, ReactNode } from 'react';
import { CrashGameState, CrashGameContextType } from '@/types';
import { useUniversalGame } from './UniversalGameContext';
import { useAuth } from './AuthContext';

const CrashGameContext = createContext<CrashGameContextType | undefined>(undefined);

interface CrashGameProviderProps {
  children: ReactNode;
}

export function CrashGameProvider({ children }: CrashGameProviderProps) {
  const universalGame = useUniversalGame();
  const { user } = useAuth();
  
  // Real-time state for crash game
  const [currentMultiplier, setCurrentMultiplier] = useState(1.0);
  const [timeElapsed, setTimeElapsed] = useState(0);
  const [roundPhase, setRoundPhase] = useState<'betting' | 'flying' | 'crashed' | 'waiting'>('waiting');
  const [timeUntilNextRound, setTimeUntilNextRound] = useState(0);
  
  // Refs for intervals
  const gameLoopRef = useRef<NodeJS.Timeout | null>(null);
  const roundTimerRef = useRef<NodeJS.Timeout | null>(null);
  
  // Ensure we're working with a crash game
  const crashGame = universalGame.currentGameType === 'crash'
    ? universalGame.currentGame as CrashGameState | null
    : null;

  const crashHistory = universalGame.gameHistory.filter(
    game => game.game_type === 'crash'
  ) as CrashGameState[];

  /**
   * Start a new crash game
   */
  const startGame = async (betAmount: number, autoCashOut?: number): Promise<boolean> => {
    return universalGame.startGame('crash', {
      bet_amount: betAmount,
      auto_cash_out: autoCashOut
    });
  };

  /**
   * Cash out the current crash game
   */
  const cashOut = async (): Promise<{ success: boolean; profit: number; multiplier: number }> => {
    try {
      const result = await universalGame.cashOut();
      return {
        success: result.success,
        profit: result.profit,
        multiplier: currentMultiplier
      };
    } catch (error) {
      console.error('Cash out error:', error);
      return {
        success: false,
        profit: 0,
        multiplier: currentMultiplier
      };
    }
  };

  /**
   * Reset the game state
   */
  const resetGame = () => {
    universalGame.resetGame();
    setCurrentMultiplier(1.0);
    setTimeElapsed(0);
    setRoundPhase('waiting');
    setTimeUntilNextRound(0);
  };

  /**
   * Switch to crash game mode
   */
  const switchToCrash = () => {
    universalGame.switchGame('crash');
  };

  /**
   * Load crash game history
   */
  const loadGameHistory = async () => {
    await universalGame.loadGameHistory('crash');
  };

  /**
   * Check if player can cash out
   */
  const canCashOut = (): boolean => {
    return crashGame?.status === 'active' && 
           roundPhase === 'flying' && 
           !crashGame?.cashed_out;
  };

  /**
   * Check if player can place a bet
   */
  const canPlaceBet = (): boolean => {
    return roundPhase === 'betting' && !crashGame;
  };

  /**
   * Get current multiplier
   */
  const getCurrentMultiplier = (): number => {
    return currentMultiplier;
  };

  /**
   * Get time elapsed in current round
   */
  const getTimeElapsed = (): number => {
    return timeElapsed;
  };

  /**
   * Get current round phase
   */
  const getRoundPhase = () => {
    return roundPhase;
  };

  /**
   * Get time until next round
   */
  const getTimeUntilNextRound = (): number => {
    return timeUntilNextRound;
  };

  /**
   * Get crash game statistics
   */
  const getCrashStats = () => {
    if (!crashGame) return null;

    return {
      betAmount: crashGame.bet_amount,
      currentMultiplier: currentMultiplier,
      potentialPayout: crashGame.bet_amount * currentMultiplier,
      phase: roundPhase,
      timeElapsed: timeElapsed,
      autoCashOut: crashGame.auto_cash_out,
      profit: crashGame.profit,
      status: crashGame.status
    };
  };

  /**
   * Start the game loop for real-time updates
   */
  const startGameLoop = () => {
    if (gameLoopRef.current) {
      clearInterval(gameLoopRef.current);
    }

    gameLoopRef.current = setInterval(() => {
      if (roundPhase === 'flying' && crashGame) {
        const newTimeElapsed = timeElapsed + 50; // Update every 50ms
        setTimeElapsed(newTimeElapsed);
        
        // Calculate new multiplier
        const newMultiplier = Math.pow(1.002, newTimeElapsed);
        setCurrentMultiplier(Math.round(newMultiplier * 100) / 100);
        
        // Check if we've hit the crash point
        if (crashGame.crash_point && newMultiplier >= crashGame.crash_point) {
          setRoundPhase('crashed');
          stopGameLoop();
          startRoundTimer();
        }
        
        // Check for auto cash out
        if (crashGame.auto_cash_out && 
            newMultiplier >= crashGame.auto_cash_out && 
            !crashGame.cashed_out) {
          cashOut();
        }
      }
    }, 50); // Update every 50ms for smooth animation
  };

  /**
   * Stop the game loop
   */
  const stopGameLoop = () => {
    if (gameLoopRef.current) {
      clearInterval(gameLoopRef.current);
      gameLoopRef.current = null;
    }
  };

  /**
   * Start round timer for betting/waiting phases
   */
  const startRoundTimer = () => {
    if (roundTimerRef.current) {
      clearTimeout(roundTimerRef.current);
    }

    // After crash, wait 3 seconds then start betting phase
    if (roundPhase === 'crashed') {
      setTimeUntilNextRound(3000);
      roundTimerRef.current = setTimeout(() => {
        setRoundPhase('betting');
        setTimeUntilNextRound(5000);
        setCurrentMultiplier(1.0);
        setTimeElapsed(0);
        
        // After 5 seconds of betting, start flying phase
        roundTimerRef.current = setTimeout(() => {
          setRoundPhase('flying');
          setTimeUntilNextRound(0);
          startGameLoop();
        }, 5000);
      }, 3000);
    }
  };

  /**
   * Initialize crash game rounds
   */
  useEffect(() => {
    // Start with betting phase
    setRoundPhase('betting');
    setTimeUntilNextRound(5000);
    
    // Start the round cycle
    const initialTimer = setTimeout(() => {
      setRoundPhase('flying');
      setTimeUntilNextRound(0);
      startGameLoop();
    }, 5000);

    return () => {
      clearTimeout(initialTimer);
      stopGameLoop();
      if (roundTimerRef.current) {
        clearTimeout(roundTimerRef.current);
      }
    };
  }, []);

  /**
   * Handle game state changes
   */
  useEffect(() => {
    if (crashGame) {
      if (crashGame.phase) {
        setRoundPhase(crashGame.phase);
      }
      if (crashGame.time_elapsed !== undefined) {
        setTimeElapsed(crashGame.time_elapsed);
      }
      if (crashGame.current_multiplier) {
        setCurrentMultiplier(crashGame.current_multiplier);
      }
    }
  }, [crashGame]);

  const value: CrashGameContextType = {
    gameState: crashGame,
    gameHistory: crashHistory,
    loading: universalGame.loading,
    error: universalGame.error,
    startGame,
    cashOut,
    resetGame,
    makeMove: cashOut, // Alias for consistency

    // Crash-specific methods
    switchToCrash,
    loadGameHistory,
    canCashOut,
    canPlaceBet,
    getCurrentMultiplier,
    getTimeElapsed,
    getRoundPhase,
    getTimeUntilNextRound,
    getCrashStats
  };

  return (
    <CrashGameContext.Provider value={value}>
      {children}
    </CrashGameContext.Provider>
  );
}

export function useCrashGame() {
  const context = useContext(CrashGameContext);
  if (context === undefined) {
    throw new Error('useCrashGame must be used within a CrashGameProvider');
  }
  return context;
}
